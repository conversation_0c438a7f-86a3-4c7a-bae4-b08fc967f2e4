<template>
  <div class="md-cart">
    <div class="md-section-header">
      <h1 class="md-section-header__title">Shopping Cart</h1>
      <p class="md-section-header__subtitle">Review your items before checkout</p>
    </div>

    <div class="md-cart__container">
      <div class="md-cart__items">
        <!-- Loading state -->
        <div v-if="isLoading" class="md-loading-state">
          <div class="md-loading-spinner"></div>
          <p>Loading your cart...</p>
        </div>

        <!-- Empty cart state -->
        <div v-else-if="!cartDetails.length" class="md-empty-state">
          <i class="material-icons md-empty-state__icon">shopping_cart</i>
          <h3 class="md-empty-state__title">Your cart is empty</h3>
          <p class="md-empty-state__text">Add some delicious items from our menu!</p>
          <router-link to="/menu" class="md-button md-button--primary">
            <i class="material-icons">restaurant_menu</i>
            Browse Menu
          </router-link>
        </div>

        <!-- Cart items -->
        <transition-group v-else name="md-list" tag="div" class="md-cart__list">
          <div v-for="(food, index) in cartDetails" :key="food.food_id" class="md-cart-item">
            <div class="md-cart-item__media">
              <img :src="require(`../assets/images/${food.food_src}`)" :alt="food.food_name">
            </div>
            <div class="md-cart-item__content">
              <h3 class="md-cart-item__title">{{ food.food_name }}</h3>
              <p class="md-cart-item__description">{{ food.food_desc }}</p>

              <div class="md-cart-item__actions">
                <div class="md-quantity-field">
                  <button
                    class="md-button md-button--icon"
                    @click="decreaseQty(index)"
                  >
                    <i class="material-icons">remove</i>
                  </button>
                  <span class="md-quantity-field__value">{{ food.item_qty }}</span>
                  <button
                    class="md-button md-button--icon"
                    @click="increaseQty(index)"
                  >
                    <i class="material-icons">add</i>
                  </button>
                </div>

                <div class="md-cart-item__price">
                  <span class="md-cart-item__current-price">
                    ${{ (parseFloat(food.food_price) - parseFloat(food.food_discount)) * food.item_qty }}
                  </span>
                  <span v-if="parseFloat(food.food_discount) > 0" class="md-cart-item__original-price">
                    ${{ parseFloat(food.food_price) * food.item_qty }}
                  </span>
                </div>

                <button class="md-button md-button--icon md-button--error" @click="removeBtn(index)">
                  <i class="material-icons">delete</i>
                </button>
              </div>
            </div>
          </div>
        </transition-group>
      </div>

      <div class="md-cart__summary">
        <div class="md-summary-card">
          <h3 class="md-summary-card__title">Order Summary</h3>

          <div class="md-summary-card__row">
            <span>Subtotal</span>
            <span>${{ calculateSummaryPrice()[0] }}</span>
          </div>

          <div class="md-summary-card__row">
            <span>Discount</span>
            <span>-${{ calculateSummaryPrice()[1] }}</span>
          </div>

          <div class="md-summary-card__row">
            <span>Delivery Fee</span>
            <span>${{ calculateSummaryPrice()[2] }}</span>
          </div>

          <!-- Promotion code input -->
          <div class="md-summary-card__promo">
            <input
              type="text"
              v-model="promoCode"
              placeholder="Enter promo code"
              class="md-summary-card__promo-input"
            >
            <button
              class="md-button md-button--small"
              @click="applyPromoCode"
              :disabled="!promoCode"
            >
              Apply
            </button>
          </div>

          <!-- Active promotion display -->
          <div v-if="$store.state.activePromotion" class="md-summary-card__active-promo">
            <div class="md-summary-card__active-promo-info">
              <span class="md-summary-card__active-promo-code">{{ $store.state.activePromotion.code }}</span>
              <span class="md-summary-card__active-promo-desc">{{ $store.state.activePromotion.description }}</span>
            </div>
            <button
              class="md-button md-button--icon md-button--small"
              @click="removePromoCode"
            >
              <i class="material-icons">close</i>
            </button>
          </div>

          <!-- Promotion discount row (only shown when promotion is active) -->
          <div v-if="$store.state.activePromotion" class="md-summary-card__row md-summary-card__row--promo">
            <span>Promo Discount</span>
            <span>-${{ calculatePromoDiscount() }}</span>
          </div>

          <div class="md-summary-card__row md-summary-card__row--total">
            <span>Total</span>
            <span>${{ calculateFinalTotal() }}</span>
          </div>

          <button
            class="md-button md-button--primary md-summary-card__action"
            :disabled="!cartDetails.length"
            @click="checkOutBtn()"
          >
            <i class="material-icons">shopping_cart_checkout</i>
            Proceed to Checkout
          </button>

          <button
            type="submit"
            class="md-button md-button--primary md-summary-card__action"
            :disabled="!cartDetails.length || !user"
            @click="placeOrderBtn()"
          >
            <i class="material-icons">payment</i>
            Place Order
          </button>

          <button
            class="md-button md-button--outline md-summary-card__action"
            :disabled="!cartDetails.length"
            @click="cancelBtn()"
          >
            <i class="material-icons">backspace</i>
            Clear Cart
          </button>
        </div>

        <div class="md-support-card">
          <i class="material-icons md-support-card__icon">support_agent</i>
          <h3 class="md-support-card__title">Need Help?</h3>
          <p class="md-support-card__text">
            We're here 24/7 to assist you with your order.
          </p>
          <a href="tel:+841231231234" class="md-support-card__phone">
            <span class="material-icons">phone</span>
            +84 ************
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { mapState } from "vuex";
import apiService from "../services/ApiService";

export default {
    name: "Cart",

    data() {
        return {
            cartItem: [],
            itemQuantity: [],
            cartDetails: [], // Add this to store the full cart item details
            isLoading: true,
            loadingTimeout: null,
            promoCode: '',
        };
    },

    mounted() {
        this.getAllCartItem();
        // Add a loading timeout as a safety measure
        this.loadingTimeout = setTimeout(() => {
            if (this.isLoading) {
                console.log('Loading timeout triggered, forcing loading state to complete');
                this.isLoading = false;
            }
        }, 5000); // 5 second timeout
    },

    beforeUnmount() {
        // Clear the timeout when component is destroyed
        if (this.loadingTimeout) {
            clearTimeout(this.loadingTimeout);
        }
    },

    computed: {
        ...mapState(["allFoods", "user"]),

        filterFoods: function () {
            // If we have detailed cart data, use that directly instead of filtering
            if (this.cartDetails && this.cartDetails.length > 0) {
                return this.cartDetails;
            }

            // Fallback to the old filtering logic
            return this.allFoods.filter(
                (f) => this.matchID(f, this.cartItem)
            );
        },
    },

    methods: {
        // Apply promotion code
        async applyPromoCode() {
            if (!this.promoCode) return;

            try {
                await this.$store.dispatch('applyPromotion', this.promoCode.trim());
                this.promoCode = ''; // Clear input after successful application
            } catch (error) {
                console.error('Failed to apply promotion:', error);
            }
        },

        // Remove promotion code
        removePromoCode() {
            this.$store.commit('removePromotion');
        },
        matchID(food, cartArray) {
            for (let i = 0; i < cartArray.length; i++) {
                if (food === cartArray[i] || food.food_id === cartArray[i]) {
                    return true;
                }
            }
            return false;
        },

        calculateItemPrice(index) {
            return (parseFloat(this.filterFoods[index].food_price) - parseFloat(this.filterFoods[index].food_discount)) * this.itemQuantity[index];
        },

        calculateSummaryPrice() {
            let initial = 0, discount = 0;

            // Make sure we have cartDetails before processing
            if (this.cartDetails && this.cartDetails.length > 0) {
                for (let i = 0; i < this.cartDetails.length; i++) {
                    // Make sure to convert string values to numbers
                    const price = parseFloat(this.cartDetails[i].food_price) || 0;
                    const discountAmount = parseFloat(this.cartDetails[i].food_discount) || 0;
                    const quantity = parseInt(this.cartDetails[i].item_qty) || 0;

                    initial += price * quantity;
                    discount += discountAmount * quantity;

                    // Debug log to check values
                    console.log(`Item ${i}: price=${price}, discount=${discountAmount}, qty=${quantity}`);
                }
            }

            // default delivery is 5.00
            let delivery = this.cartDetails && this.cartDetails.length > 0 ? 5 : 0;
            // initial - discount + delivery
            let total = (initial - discount) + delivery;

            // Debug log for final calculation
            console.log(`Summary: initial=${initial}, discount=${discount}, delivery=${delivery}, total=${total}`);

            return [initial.toFixed(2), discount.toFixed(2), delivery.toFixed(2), total.toFixed(2)];
        },

        calculatePromoDiscount() {
            // If no active promotion, return 0
            if (!this.$store.state.activePromotion) return '0.00';

            const [subtotal, , delivery] = this.calculateSummaryPrice().map(val => parseFloat(val));
            const promotion = this.$store.state.activePromotion;

            // If discount is a percentage (less than 1)
            if (promotion.discount < 1) {
                return (subtotal * promotion.discount).toFixed(2);
            }
            // If discount is a fixed amount (like free shipping)
            else {
                return Math.min(promotion.discount, delivery).toFixed(2);
            }
        },

        calculateFinalTotal() {
            const [subtotal, itemDiscount, delivery] = this.calculateSummaryPrice().map(val => parseFloat(val));
            const promoDiscount = parseFloat(this.calculatePromoDiscount());

            // Calculate final total: subtotal - item discounts - promo discount + delivery
            const total = (subtotal - itemDiscount - promoDiscount + delivery).toFixed(2);
            return total;
        },

        async onQtyChange(e, i) {
            if (e.target.value < 1) {
                e.target.value = 1;
                this.cartDetails[i].item_qty = 1;
            }

            let data = {
                user_id: this.user.user_id,
                food_id: parseInt(this.cartDetails[i].food_id),
                item_qty: parseInt(this.cartDetails[i].item_qty)
            };

            try {
                await axios.put("/api/cartItem/", data);
            } catch (error) {
                console.error('Error updating cart item quantity:', error);
            }
        },

        async cancelBtn() {
            try {
                await axios.delete("/api/cartItem/" + this.user.user_id);
                this.cartItem = [];
                this.itemQuantity = [];
                this.cartDetails = [];
            } catch (error) {
                console.error('Error clearing cart:', error);
            }
        },

        checkOutBtn() {
            this.$router.push("/checkout");
        },

        async placeOrderBtn() {
            // Check if user is logged in
            if (!this.user) {
                alert('Please log in to place an order');
                this.$router.push('/login');
                return;
            }

            // Check if cart is empty
            if (!this.cartDetails.length) {
                alert('Your cart is empty');
                return;
            }

            try {
                // Calculate prices
                const [, discount, delivery, total] = this.calculateSummaryPrice();

                // Get user's default address if available
                let userAddresses = [];
                let userAddress = '';

                try {
                    userAddresses = await apiService.getUserAddresses(this.user.user_id);
                    const defaultAddress = userAddresses.find(addr => addr.is_default) || userAddresses[0];

                    if (defaultAddress) {
                        userAddress = `${defaultAddress.street}, ${defaultAddress.city}, ${defaultAddress.zipCode}`;
                    }
                } catch (error) {
                    console.error('Error fetching user addresses:', error);
                }

                if (!userAddress) {
                    alert('Please add a delivery address first. Redirecting to checkout...');
                    this.$router.push('/checkout');
                    return;
                }

                // Create order data object
                const orderData = {
                    userId: parseInt(this.user.user_id),
                    items: this.cartDetails,
                    phone: this.user.user_phone || '',
                    address: userAddress,
                    paymentMethod: 'cash', // Default to cash payment
                    discount: discount,
                    delivery: delivery,
                    total: total,
                    isPaid: false // Default to not paid for cash on delivery
                };

                // Use the ApiService to place the order
                if (confirm('Are you sure you want to place this order with cash on delivery?')) {
                    alert('Processing your order. Please wait...');

                    const orderResult = await apiService.placeOrder(orderData);
                    console.log('Order placed successfully:', orderResult);

                    // Clear local cart data
                    this.cartItem = [];
                    this.itemQuantity = [];
                    this.cartDetails = [];

                    // Navigate to thank you page
                    this.$router.push({
                        path: "/thank",
                        query: { order_id: orderResult.orderId }
                    });
                }
            } catch (error) {
                console.error('Error processing order:', error);
                alert('There was an error processing your order. Please try again.');
            }
        },

        async removeBtn(index) {
            try {
                await axios.delete("/api/cartItem/" + this.user.user_id + "/" + this.cartDetails[index].food_id);
                this.cartDetails.splice(index, 1);
            } catch (error) {
                console.error('Error removing item from cart:', error);
            }
        },

        async getAllCartItem() {
            if (this.user) {
                try {
                    this.isLoading = true;
                    let existItem = await axios.get('/api/cartItem/' + this.user.user_id);
                    this.cartItem = [];
                    this.itemQuantity = [];
                    this.cartDetails = [];

                    // Process cart data - now includes food details from JOIN query
                    if (existItem.data && existItem.data.length > 0) {
                        console.log('Cart data from server with food details:', existItem.data);

                        // Process the cart data directly since it now includes food details
                        this.cartDetails = existItem.data.map(item => ({
                            ...item,
                            food_price: parseFloat(item.food_price) || 0,
                            food_discount: parseFloat(item.food_discount) || 0,
                            item_qty: parseInt(item.item_qty) || 1
                        }));

                        // Update arrays for backward compatibility
                        this.cartItem = this.cartDetails.map(item => item.food_id);
                        this.itemQuantity = this.cartDetails.map(item => item.item_qty);

                        console.log('Processed cart details:', this.cartDetails);
                    } else {
                        console.log('Cart is empty');
                    }

                    this.isLoading = false;
                } catch (error) {
                    console.error('Error fetching cart items:', error);
                    // Initialize empty arrays as fallback
                    this.cartItem = [];
                    this.itemQuantity = [];
                    this.cartDetails = [];
                    this.isLoading = false;
                }
            } else {
                // No user logged in, ensure loading is false
                this.isLoading = false;
            }
        },

        async increaseQty(index) {
            this.cartDetails[index].item_qty++;
            await this.onQtyChange({ target: { value: this.cartDetails[index].item_qty } }, index);
        },

        async decreaseQty(index) {
            if (this.cartDetails[index].item_qty > 1) {
                this.cartDetails[index].item_qty--;
                await this.onQtyChange({ target: { value: this.cartDetails[index].item_qty } }, index);
            }
        }
    }
}
</script>

<style scoped>
.md-cart {
  padding: var(--md-sys-spacing-medium);
}

.md-cart__container {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-large);
}

.md-cart__items {
  flex: 1;
}

.md-cart__list {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-list-enter-active,
.md-list-leave-active {
  transition: all 0.3s ease;
}

.md-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.md-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.md-cart-item {
  display: flex;
  gap: var(--md-sys-spacing-medium);
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-medium);
  box-shadow: var(--md-elevation-level1);
}

.md-cart-item__media {
  width: 120px;
  height: 120px;
  border-radius: var(--md-sys-shape-corner-medium);
  overflow: hidden;
}

.md-cart-item__media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-cart-item__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-small);
}

.md-cart-item__title {
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-cart-item__description {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.md-cart-item__actions {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-medium);
  margin-top: auto;
}

.md-quantity-field {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  background-color: var(--md-sys-color-surface-variant);
  padding: var(--md-sys-spacing-small);
  border-radius: var(--md-sys-shape-corner-full);
}

.md-quantity-field__value {
  min-width: 32px;
  text-align: center;
  font-family: var(--md-sys-typescale-body-large-font);
  color: var(--md-sys-color-on-surface);
}

.md-cart-item__price {
  margin-left: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.md-cart-item__current-price {
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  color: var(--md-sys-color-primary);
}

.md-cart-item__original-price {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: line-through;
}

.md-button--error {
  color: #dc3545;
}

.md-cart__summary {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-summary-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-large);
  box-shadow: var(--md-elevation-level1);
}

.md-summary-card__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-large);
}

.md-summary-card__row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-medium);
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
}

.md-summary-card__row--total {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-primary);
  border-top: 1px solid var(--md-sys-color-outline);
  margin-top: var(--md-sys-spacing-medium);
  padding-top: var(--md-sys-spacing-medium);
}

.md-summary-card__action {
  width: 100%;
  margin-top: var(--md-sys-spacing-medium);
}

.md-summary-card__promo {
  display: flex;
  gap: var(--md-sys-spacing-small);
  margin-bottom: var(--md-sys-spacing-medium);
  padding-top: var(--md-sys-spacing-medium);
  border-top: 1px dashed var(--md-sys-color-outline);
}

.md-summary-card__promo-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-medium);
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
}

.md-button--small {
  padding: 8px 12px;
  font-size: 14px;
}

.md-summary-card__active-promo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--md-sys-color-surface-variant);
  padding: var(--md-sys-spacing-small);
  border-radius: var(--md-sys-shape-corner-medium);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-summary-card__active-promo-info {
  display: flex;
  flex-direction: column;
}

.md-summary-card__active-promo-code {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-primary);
}

.md-summary-card__active-promo-desc {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
}

.md-summary-card__row--promo {
  color: var(--md-sys-color-primary);
  font-weight: 500;
}

.md-support-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-large);
  text-align: center;
  box-shadow: var(--md-elevation-level1);
}

.md-support-card__icon {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-support-card__title {
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-small);
}

.md-support-card__text {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0 0 var(--md-sys-spacing-medium);
}

.md-support-card__phone {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
  text-decoration: none;
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
}

.md-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  color: var(--md-sys-color-on-surface-variant);
}

.md-loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--md-sys-color-surface-variant);
  border-radius: 50%;
  border-top-color: var(--md-sys-color-primary);
  margin-bottom: 16px;
  animation: md-spin 1s linear infinite;
}

@keyframes md-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Desktop styles */
@media (min-width: 769px) {
  .md-cart {
    padding: var(--md-sys-spacing-large);
  }

  .md-cart__container {
    flex-direction: row;
    align-items: start;
  }

  .md-cart__summary {
    width: 360px;
    position: sticky;
    top: var(--md-sys-spacing-large);
  }
}
</style>