<template>
  <div class="md-menu">
    <!-- Search and Filter Bar -->
    <div class="md-search-filter">
      <div class="md-search-bar__container">
        <span class="material-icons-round">search</span>
        <input
          type="text"
          v-model="foodObj.name"
          placeholder="Search dishes..."
          class="md-search-bar__input"
        />
      </div>
      <button class="md-filter-button" @click="displayFilterDrop">
        <span class="material-icons-round">tune</span>
        <span>Filter</span>
      </button>
    </div>

    <!-- Category Chips -->
    <div class="md-category-chips">
      <button
        v-for="category in categories"
        :key="category"
        :class="['md-chip', {'md-chip--selected': foodObj.category === category}]"
        @click="filterFoodBtn($event)"
        :value="category"
      >
        {{ category }}
      </button>
    </div>

    <!-- Filter Panel -->
    <div v-if="showDropDown" class="md-filter-panel">
      <div class="md-filter-section">
        <h3 class="md-filter-section__title">Status</h3>
        <div class="md-checkbox-group">
          <label v-for="status in statuses" :key="status.id" class="md-checkbox">
            <input
              type="checkbox"
              :id="status.id"
              :value="status.value"
              v-model="foodObj.status"
            >
            <span class="md-checkbox__label">{{ status.label }}</span>
          </label>
        </div>
      </div>

      <div class="md-filter-section">
        <h3 class="md-filter-section__title">Price Range</h3>
        <div class="md-radio-group">
          <label v-for="range in priceRanges" :key="range.id" class="md-radio">
            <input
              type="radio"
              name="priceRange"
              :id="range.id"
              :value="range.value"
              v-model="foodObj.price"
            >
            <span class="md-radio__label">{{ range.label }}</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Food Grid -->
    <div class="md-food-grid">
      <div
        v-for="(food, index) in paginatedFoods"
        :key="food.food_id || index"
        class="md-food-card"
      >
        <div class="md-food-card__image">
          <img :src="getImageSrc(food)" :alt="food.food_name">
          <div class="md-food-card__actions">
            <button @click.stop="quickView(index)" class="md-action-button">
              <span class="material-icons-round">visibility</span>
            </button>
            <router-link
              v-if="getFoodId(food)"
              :to="`/food/${getFoodId(food)}`"
              class="md-action-button"
            >
              <span class="material-icons-round">info</span>
            </router-link>
            <button
              v-else
              @click="debugFood(food)"
              class="md-action-button"
              style="background-color: #ff5722; color: white;"
            >
              <span class="material-icons-round">bug_report</span>
            </button>
          </div>
        </div>
        <div class="md-food-card__content">
          <h3 class="md-food-card__title">{{ food.food_name || 'Unnamed Product' }}</h3>
          <p class="md-food-card__description">
            {{ food.food_desc || food.food_description || food.description || "No description available" }}
          </p>
          <div class="md-food-card__price">
            <span class="md-food-card__current-price">
              ${{ parseFloat(food.food_price || food.price || 0) - parseFloat(food.food_discount || 0) }}
            </span>
            <span v-if="parseFloat(food.food_discount || 0) > 0" class="md-food-card__original-price">
              ${{ parseFloat(food.food_price || food.price || 0) }}
            </span>
          </div>
          <div class="md-food-card__buttons">
            <button class="md-button md-button--filled" @click="addItem(index)">
              <span class="material-icons-round">add_shopping_cart</span>
              Add to Cart
            </button>
            <router-link
              v-if="getFoodId(food)"
              :to="`/food/${getFoodId(food)}`"
              class="md-button md-button--outlined"
            >
              <span class="material-icons-round">info</span>
              Details
            </router-link>
            <button
              v-else
              @click="debugFood(food)"
              class="md-button md-button--outlined"
              style="border-color: #ff5722; color: #ff5722;"
            >
              <span class="material-icons-round">bug_report</span>
              Debug
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!filterFoods.length" class="md-empty-state">
        <span class="material-icons-round md-empty-state__icon">search_off</span>
        <h3 class="md-empty-state__title">No results found</h3>
        <p class="md-empty-state__text">Try adjusting your search or filters</p>
      </div>
    </div>

    <!-- Pagination -->
    <div class="md-pagination">
      <button
        class="md-pagination__button"
        @click="prevPage"
        :disabled="pageNum === 0"
      >
        <span class="material-icons-round">chevron_left</span>
      </button>
      <div class="md-pagination__pages">
        <button
          v-for="n in Math.ceil(filterFoods.length / perPage)"
          :key="n"
          :class="['md-pagination__page', {'md-pagination__page--active': pageNum === n - 1}]"
          @click="pageNum = n - 1"
        >
          {{ n }}
        </button>
      </div>
      <button
        class="md-pagination__button"
        @click="nextPage"
        :disabled="pageNum >= Math.ceil(filterFoods.length / perPage) - 1"
      >
        <span class="material-icons-round">chevron_right</span>
      </button>
    </div>

    <!-- Quick View Dialog -->
    <QuickView v-if="showQuickView" :food="sendId" @close="closeView">
      <button class="md-icon-button" @click="closeView"><span class="material-icons">close</span></button>
    </QuickView>
  </div>
</template>

<script>
import QuickView from "@/components/QuickView.vue";
import { mapState } from "vuex";
import axios from "axios";

export default {
  name: "MenuPage",
  components: { QuickView },

  data() {
    return {
      foodObj: { name: "", category: "", status: [], price: "", type: "" },
      showQuickView: false,
      showDropDown: false,
      sendId: null,
      perPage: 6,
      pageNum: 0,
      categories: ["all", "taco", "burrito", "nachos", "sides", "dessert", "drink"],
      statuses: [
        { id: "bsStatus", value: "Best Seller", label: "Best Seller" },
        { id: "newStatus", value: "New", label: "New" },
        { id: "saleStatus", value: "Sale", label: "On Sale" }
      ],
      priceRanges: [
        { id: "ltPrice", value: "2", label: "Under $2" },
        { id: "btPrice", value: "2-12", label: "$2 - $12" },
        { id: "mtPrice", value: "12", label: "Over $12" }
      ]
    };
  },

  watch: {
    // Watch for route query changes to update category filter
    '$route.query.category': {
      immediate: true,
      handler(category) {
        if (category) {
          this.foodObj.category = category;
        }
      }
    },
    // Watch for search query changes from URL
    '$route.query.search': {
      immediate: true,
      handler(searchQuery) {
        if (searchQuery) {
          this.foodObj.name = searchQuery;
        }
      }
    }
  },

  computed: {
    ...mapState(["allFoods"]),

    filterFoods() {
      if (!this.allFoods || this.allFoods.length === 0) return [];

      return this.allFoods.filter((f) => {
        // Handle both API data format and mock data format
        const foodName = f.food_name || f.name || '';
        const foodCategory = f.food_category || f.category || '';
        const foodType = f.food_type || f.type || '';
        const filterCategory = this.foodObj.category;

        // Inline category matching function
        const matchesCategory = (() => {
          // If no filter or 'all' is selected, show everything
          if (!filterCategory || filterCategory === 'all' || filterCategory === '') {
            return true;
          }

          // Convert both to lowercase for case-insensitive comparison
          const foodCategoryLower = foodCategory.toLowerCase();
          const filterCategoryLower = filterCategory.toLowerCase();

          // Handle special cases and partial matches
          if (filterCategoryLower === 'taco' && foodCategoryLower.includes('taco')) {
            return true;
          } else if (filterCategoryLower === 'burrito' && foodCategoryLower.includes('burrito')) {
            return true;
          } else if (filterCategoryLower === 'nachos' && foodCategoryLower.includes('nacho')) {
            return true;
          } else if (filterCategoryLower === 'side' && (foodCategoryLower.includes('side') || foodCategoryLower.includes('salad'))) {
            return true;
          } else if (filterCategoryLower === 'dessert' && foodCategoryLower.includes('dessert')) {
            return true;
          } else if (filterCategoryLower === 'drink' && (foodCategoryLower.includes('drink') || foodCategoryLower.includes('beverage'))) {
            return true;
          }

          // Default case: check if categories match exactly
          return foodCategoryLower === filterCategoryLower;
        })();

        return foodName.toLowerCase().includes(this.foodObj.name.toLowerCase()) &&
          matchesCategory &&
          (this.evaluatePrice(f, this.foodObj.price)) &&
          foodType.toLowerCase().includes(this.foodObj.type.toLowerCase()) &&
          (this.evaluateStatus(f, this.foodObj.status));
      });
    },

    paginatedFoods() {
      return this.filterFoods.slice(
        this.pageNum * this.perPage,
        this.pageNum * this.perPage + this.perPage
      );
    },

    calculatePages() {
      return Math.ceil(this.filterFoods.length / this.perPage);
    }
  },

  async created() {
    // Load foods from the store
    await this.$store.dispatch("getFoodsData");

    // Debug: Log the first few food items to check data structure
    if (this.allFoods && this.allFoods.length > 0) {
      console.log('=== MENU DEBUG: First 3 food items ===');
      this.allFoods.slice(0, 3).forEach((food, index) => {
        console.log(`Food ${index + 1}:`, food);
        console.log(`  - food_id: ${food.food_id} (${typeof food.food_id})`);
        console.log(`  - food_name: ${food.food_name}`);
        console.log(`  - Keys: ${Object.keys(food).join(', ')}`);
      });
      console.log('=====================================');
    }
  },

  methods: {
    displayFilterDrop() {
      this.showDropDown = !this.showDropDown;
    },

    quickView(index) {
      const food = this.paginatedFoods[index];
      const foodId = this.getFoodId(food);

      if (!foodId) {
        console.error('Food ID is missing, cannot show quick view. Food object:', food);
        this.debugFood(food);
        return;
      }

      this.sendId = foodId;
      this.showQuickView = true;
    },

    prevPage() {
      if (this.pageNum > 0) this.pageNum--;
    },

    nextPage() {
      if (this.pageNum < this.calculatePages - 1) this.pageNum++;
    },

    async addItem(index) {
      if (!this.$store.state.user) {
        // Redirect to login if user is not logged in
        this.$router.push('/login');
        return;
      }

      const food = this.paginatedFoods[index];
      console.log('Food item being added:', food); // Debug log

      let foodId = this.getFoodId(food);

      if (!foodId) {
        console.error('Food ID is missing, cannot add to cart. Food object:', food);
        this.debugFood(food);
        alert('Could not add item to cart: Missing food ID');
        return;
      }

      try {
        const userId = this.$store.state.user.user_id;
        console.log('Adding to cart:', { userId, foodId });

        // Get fresh food data to ensure we have the correct ID
        try {
          const freshFoodData = await axios.get(`/api/foods/${foodId}`);
          if (freshFoodData.data && freshFoodData.data.food_id) {
            // Update with the confirmed food_id from the API
            foodId = freshFoodData.data.food_id;
            console.log('Retrieved fresh food data:', freshFoodData.data);
          }
        } catch (foodError) {
          console.warn('Could not fetch fresh food data, using existing ID:', foodError);
        }

        // First check if item exists in cart
        try {
          const existingItem = await axios.get(`/api/cartItem/${userId}/${foodId}`);
          if (existingItem.data && existingItem.data.length > 0) {
            // Update quantity if already in cart
            const newQty = parseInt(existingItem.data[0].item_qty) + 1;
            const result = await axios.put("/api/cartItem/", {
              user_id: userId,
              food_id: parseInt(foodId),
              item_qty: newQty
            });
            console.log('Updated existing cart item:', result.data);
          } else {
            // Add new item if not in cart
            const result = await axios.post("/api/cartItem", {
              user_id: userId,
              food_id: parseInt(foodId),
              item_qty: 1
            });
            console.log('Added new cart item:', result.data);
          }
          // Show success notification (optional)
          alert('Item added to cart!');
        } catch (error) {
          console.log('Attempting to add new item to cart', error);
          // Item doesn't exist in cart, add it
          const result = await axios.post("/api/cartItem", {
            user_id: userId,
            food_id: parseInt(foodId),
            item_qty: 1
          });
          console.log('Added new cart item:', result.data);
          // Show success notification (optional)
          alert('Item added to cart!');
        }
      } catch (error) {
        console.error('Error adding item to cart:', error);
        alert('Failed to add item to cart');
      }
    },

    closeView() {
      this.showQuickView = false;
      this.sendId = null;
    },

    filterFoodBtn(event) {
      this.foodObj.category = event.target.value;
      this.pageNum = 0;
    },

    evaluatePrice(food, price) {
      if (!price) return true;

      // Handle both API data format and mock data format
      const foodBasePrice = food.food_price || food.price || 0;
      const foodDiscount = food.food_discount || 0;
      const foodPrice = parseFloat(foodBasePrice) - parseFloat(foodDiscount);

      if (price === "2") return foodPrice < 2;
      if (price === "2-12") return foodPrice >= 2 && foodPrice <= 12;
      if (price === "12") return foodPrice > 12;
      return true;
    },

    evaluateStatus(food, statusArray) {
      if (!statusArray.length) return true;

      return statusArray.some(status => {
        // Handle both API data format and mock data format
        const foodStatus = food.food_status || food.status || '';
        const foodDiscount = food.food_discount || 0;

        if (status === "Best Seller") return foodStatus.toLowerCase().includes("best seller");
        if (status === "New") return foodStatus.toLowerCase().includes("new");
        if (status === "Sale") return parseFloat(foodDiscount) > 0;
        return false;
      });
    },

    getImageSrc(food) {
      // First, handle null/undefined data gracefully
      if (!food) {
        return require('../assets/images/nachos-img.png');
      }

      // Handle image path formats from both API and mock data
      const imageName = food.food_image || food.image;

      // If it's a full URL, use it directly
      if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
        return imageName;
      }

      // Handle cases where database returns 'food-img.png' which doesn't exist
      if (imageName === 'food-img.png') {
        // Get category-based default instead
        const category = (food.food_category || food.category || 'other').toLowerCase();
        return this.getCategoryDefaultImage(category);
      }

      // Use a safer approach with a default fallback image
      if (imageName) {
        try {
          return require(`../assets/images/${imageName}`);
        } catch (error) {
          // Image not found, will select category-based default
          console.log('Image not found:', imageName);
        }
      }

      // Select a default image based on category
      const category = (food.food_category || food.category || 'other').toLowerCase();
      return this.getCategoryDefaultImage(category);
    },

    // Helper method to get default images by category
    getCategoryDefaultImage(category) {
      // These are guaranteed to exist in our assets
      if (category.includes('taco')) {
        return require('../assets/images/taco-img.png');
      } else if (category.includes('burrito')) {
        return require('../assets/images/burrito-img.png');
      } else if (category.includes('nacho')) {
        return require('../assets/images/nachos-img.png');
      } else if (category.includes('side') || category.includes('salad')) {
        return require('../assets/images/salad-img.png');
      } else if (category.includes('dessert')) {
        return require('../assets/images/dessert-img.png');
      } else if (category.includes('coca') || category.includes('drink')) {
        return require('../assets/images/coca-img.png');
      }

      // Default fallback image for any other category
      return require('../assets/images/nachos-img.png');
    },

    // Helper method to safely get food ID
    getFoodId(food) {
      if (!food) return null;

      // Try different possible ID fields
      const foodId = food.food_id || food.id || (food._id ? food._id.toString() : null);

      // Ensure it's a valid ID (not undefined, null, 0, or empty string)
      if (foodId && foodId !== 'undefined' && foodId !== '0' && foodId !== '') {
        return foodId;
      }

      return null;
    },

    // Debug method to help identify data issues
    debugFood(food) {
      console.log('=== FOOD DEBUG INFO ===');
      console.log('Full food object:', food);
      console.log('food.food_id:', food.food_id);
      console.log('food.id:', food.id);
      console.log('food._id:', food._id);
      console.log('typeof food.food_id:', typeof food.food_id);
      console.log('All food keys:', Object.keys(food));
      console.log('======================');

      alert(`Food Debug Info:\nName: ${food.food_name || 'Unknown'}\nID: ${food.food_id || 'Missing'}\nType: ${typeof food.food_id}\nCheck console for full details.`);
    }
  }
};
</script>

<style scoped>
.md-menu {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-large);
  padding: var(--md-sys-spacing-medium);
}

.md-search-filter {
  display: flex;
  gap: var(--md-sys-spacing-medium);
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: var(--md-sys-color-background);
  padding: var(--md-sys-spacing-medium) 0;
}

.md-filter-button {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  cursor: pointer;
  box-shadow: var(--md-elevation-level1);
}

.md-category-chips {
  display: flex;
  gap: var(--md-sys-spacing-small);
  overflow-x: auto;
  padding: var(--md-sys-spacing-small) 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.md-category-chips::-webkit-scrollbar {
  display: none;
}

.md-chip {
  flex: 0 0 auto;
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  cursor: pointer;
  transition: all 0.2s ease;
}

.md-chip--selected {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-filter-panel {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-large);
  box-shadow: var(--md-elevation-level2);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-filter-section {
  margin-bottom: var(--md-sys-spacing-large);
}

.md-filter-section__title {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-on-surface);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-food-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--md-sys-spacing-medium);
  margin-bottom: var(--md-sys-spacing-large);
}

.md-food-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  box-shadow: var(--md-elevation-level1);
  transition: transform 0.2s ease;
}

.md-food-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--md-elevation-level2);
}

.md-food-card__image {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: var(--md-sys-shape-corner-medium) var(--md-sys-shape-corner-medium) 0 0;
}

.md-food-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.md-food-card:hover .md-food-card__image img {
  transform: scale(1.05);
}

.md-food-card__actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  opacity: 0;
  transform: translateX(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.md-food-card:hover .md-food-card__actions {
  opacity: 1;
  transform: translateX(0);
}

.md-action-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--md-sys-color-primary);
  transition: background-color 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.md-action-button:hover {
  background-color: white;
}

.md-food-card__content {
  padding: var(--md-sys-spacing-medium);
}

.md-food-card__title {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-food-card__description {
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  color: var(--md-sys-color-on-surface-variant);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 2.5rem;
}

.md-food-card__price {
  display: flex;
  align-items: baseline;
  margin-bottom: 1rem;
}

.md-food-card__current-price {
  font-weight: 600;
  color: var(--md-sys-color-primary);
  font-size: 1.125rem;
}

.md-food-card__original-price {
  margin-left: 0.5rem;
  text-decoration: line-through;
  color: var(--md-sys-color-outline);
  font-size: 0.875rem;
}

.md-food-card__buttons {
  display: flex;
  gap: 0.5rem;
}

.md-button--outlined {
  background-color: transparent;
  border: 1px solid var(--md-sys-color-primary);
  color: var(--md-sys-color-primary);
}

.md-button--outlined:hover {
  background-color: rgba(25, 118, 210, 0.05);
}

.md-empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--md-sys-spacing-large) 0;
}

.md-empty-state__icon {
  font-size: 48px;
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-empty-state__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-small);
}

.md-empty-state__text {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.md-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-small);
}

.md-pagination__pages {
  display: flex;
  gap: var(--md-sys-spacing-small);
}

.md-pagination__button,
.md-pagination__page {
  min-width: 36px;
  height: 36px;
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  cursor: pointer;
  box-shadow: var(--md-elevation-level1);
}

.md-pagination__page--active {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

/* Desktop styles */
@media (min-width: 769px) {
  .md-menu {
    padding: var(--md-sys-spacing-large);
  }

  .md-food-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}
</style>
