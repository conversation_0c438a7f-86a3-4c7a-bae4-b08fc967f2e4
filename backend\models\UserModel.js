// import connection
import db from "../config/database.js";

// get all user
export const getAllUser = (result) => {
    db.query("SELECT * FROM user", (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results[0]);
        }
    });
};


// get single user
export const getUserByEmail = (data,result) => {
    db.query("SELECT user_id, user_name, user_password, role, is_active FROM user WHERE user_email = ?",[data], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results[0]);
        }
    });
};

// insert User
export const insertUser = (data,result) => {
    // Set default values for role and is_active if not provided
    const userData = {
        ...data,
        role: data.role || 'user',
        is_active: data.is_active !== undefined ? data.is_active : true
    };
    
    db.query("INSERT INTO user SET ?", userData, (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results[0]);
        }
    });
};

// Get complete user profile
export const getUserProfile = (id, result) => {
    db.query("SELECT user_id, user_name, user_email, user_phone, user_birth, user_gender, role, is_active FROM user WHERE user_id = ?", [id], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results[0]);
        }
    });
};

// Update user profile
export const updateUserProfile = (id, data, result) => {
    db.query("UPDATE user SET ? WHERE user_id = ?", [data, id], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, { id: id, ...data });
        }
    });
};

// Create address table if it doesn't exist
db.query(`CREATE TABLE IF NOT EXISTS user_address (
    address_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    label VARCHAR(255),
    street VARCHAR(255),
    city VARCHAR(255),
    zip_code VARCHAR(20),
    is_default BOOLEAN DEFAULT false,
    FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE
)`, (err) => {
    if (err) {
        console.log("Error creating user_address table:", err);
    } else {
        console.log("user_address table ready");
    }
});

// Get user addresses
export const getUserAddresses = (userId, result) => {
    db.query("SELECT * FROM user_address WHERE user_id = ?", [userId], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            // Convert the is_default from 0/1 to boolean
            const addresses = results.map(address => ({
                ...address,
                is_default: address.is_default === 1,
                // Map database fields to frontend expected format
                id: address.address_id,
                label: address.label,
                street: address.street,
                city: address.city,
                zipCode: address.zip_code,
                isDefault: address.is_default === 1
            }));
            result(null, addresses);
        }
    });
};

// Add new address
export const addUserAddress = (userId, data, result) => {
    // If this is set as default, unset any other default address first
    if (data.isDefault) {
        db.query("UPDATE user_address SET is_default = false WHERE user_id = ?", [userId], (updateErr) => {
            if (updateErr) {
                console.log(updateErr);
                result(updateErr, null);
                return;
            }
            
            // Now insert the new address
            const addressData = {
                user_id: userId,
                label: data.label,
                street: data.street,
                city: data.city,
                zip_code: data.zipCode,
                is_default: data.isDefault
            };
            
            db.query("INSERT INTO user_address SET ?", addressData, (err, insertResult) => {
                if (err) {
                    console.log(err);
                    result(err, null);
                } else {
                    // Return the created address with id
                    result(null, {
                        id: insertResult.insertId,
                        ...data
                    });
                }
            });
        });
    } else {
        // Just insert the new address
        const addressData = {
            user_id: userId,
            label: data.label,
            street: data.street,
            city: data.city,
            zip_code: data.zipCode,
            is_default: data.isDefault
        };
        
        db.query("INSERT INTO user_address SET ?", addressData, (err, insertResult) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                // Return the created address with id
                result(null, {
                    id: insertResult.insertId,
                    ...data
                });
            }
        });
    }
};

// Update address
export const updateUserAddress = (userId, addressId, data, result) => {
    // If this is set as default, unset any other default address first
    if (data.isDefault) {
        db.query("UPDATE user_address SET is_default = false WHERE user_id = ?", [userId], (updateErr) => {
            if (updateErr) {
                console.log(updateErr);
                result(updateErr, null);
                return;
            }
            
            // Now update the address
            const addressData = {
                label: data.label,
                street: data.street,
                city: data.city,
                zip_code: data.zipCode,
                is_default: data.isDefault
            };
            
            db.query("UPDATE user_address SET ? WHERE address_id = ? AND user_id = ?", 
                [addressData, addressId, userId], (err, updateResult) => {
                if (err) {
                    console.log(err);
                    result(err, null);
                } else {
                    // Return the updated address
                    result(null, {
                        id: addressId,
                        ...data
                    });
                }
            });
        });
    } else {
        // Just update the address
        const addressData = {
            label: data.label,
            street: data.street,
            city: data.city,
            zip_code: data.zipCode,
            is_default: data.isDefault
        };
        
        db.query("UPDATE user_address SET ? WHERE address_id = ? AND user_id = ?", 
            [addressData, addressId, userId], (err, updateResult) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                // Return the updated address
                result(null, {
                    id: addressId,
                    ...data
                });
            }
        });
    }
};

// Delete address
export const deleteUserAddress = (userId, addressId, result) => {
    // First check if this is a default address
    db.query("SELECT is_default FROM user_address WHERE address_id = ? AND user_id = ?", 
        [addressId, userId], (err, checkResults) => {
        if (err) {
            console.log(err);
            result(err, null);
            return;
        }
        
        if (checkResults.length === 0) {
            result({ message: "Address not found" }, null);
            return;
        }
        
        // Delete the address
        db.query("DELETE FROM user_address WHERE address_id = ? AND user_id = ?", 
            [addressId, userId], (deleteErr, deleteResult) => {
            if (deleteErr) {
                console.log(deleteErr);
                result(deleteErr, null);
            } else {
                // If it was a default address, set a new default if possible
                if (checkResults[0].is_default) {
                    db.query("UPDATE user_address SET is_default = true WHERE user_id = ? LIMIT 1", 
                        [userId], (updateErr) => {
                        if (updateErr) {
                            console.log(updateErr);
                            // Not critical, so still return success
                        }
                        result(null, { message: "Address deleted successfully" });
                    });
                } else {
                    result(null, { message: "Address deleted successfully" });
                }
            }
        });
    });
};

// Get user orders
export const getUserOrders = (userId, result) => {
    // Join billstatus with billdetails and foods to get complete order information
    const query = `
        SELECT b.bill_id, b.bill_when as date, b.bill_total as total, b.bill_status as status,
               b.bill_paid, b.bill_address, b.bill_phone
        FROM billstatus b
        WHERE b.user_id = ?
        ORDER BY b.bill_when DESC
    `;
    
    db.query(query, [userId], (err, orders) => {
        if (err) {
            console.log(err);
            result(err, null);
            return;
        }
        
        // For each order, fetch its items
        const processedOrders = [];
        let completedOrders = 0;
        
        if (orders.length === 0) {
            // No orders found
            result(null, []);
            return;
        }
        
        orders.forEach(order => {
            const itemsQuery = `
                SELECT bd.food_id, bd.item_qty as quantity, f.food_name as name, f.food_price as price
                FROM billdetails bd
                JOIN food f ON bd.food_id = f.food_id
                WHERE bd.bill_id = ?
            `;
            
            db.query(itemsQuery, [order.bill_id], (itemsErr, items) => {
                if (itemsErr) {
                    console.log(itemsErr);
                    completedOrders++;
                } else {
                    // Map the status code to a readable status
                    let statusText = "Processing";
                    if (order.status === 2) statusText = "Delivered";
                    else if (order.status === 3) statusText = "Cancelled";
                    
                    // Format the order data for frontend
                    processedOrders.push({
                        id: order.bill_id.toString(),
                        date: order.date,
                        status: statusText,
                        total: parseFloat(order.total) || 0,
                        address: order.bill_address,
                        phone: order.bill_phone,
                        paid: order.bill_paid === "paid",
                        items: items.map(item => ({
                            id: item.food_id,
                            name: item.name,
                            quantity: item.quantity,
                            price: parseFloat(item.price) || 0
                        }))
                    });
                    completedOrders++;
                }
                
                // If all orders have been processed, return the result
                if (completedOrders === orders.length) {
                    result(null, processedOrders);
                }
            });
        });
    });
};

// Get order details
export const getOrderDetails = (userId, orderId, result) => {
    // Get the order information
    const orderQuery = `
        SELECT b.bill_id, b.bill_when as date, b.bill_total as total, b.bill_status as status,
               b.bill_paid, b.bill_address, b.bill_phone, b.bill_method, b.bill_discount, b.bill_delivery
        FROM billstatus b
        WHERE b.bill_id = ? AND b.user_id = ?
    `;
    
    db.query(orderQuery, [orderId, userId], (err, orders) => {
        if (err) {
            console.log(err);
            result(err, null);
            return;
        }
        
        if (orders.length === 0) {
            result({ message: "Order not found" }, null);
            return;
        }
        
        const order = orders[0];
        
        // Get the order items
        const itemsQuery = `
            SELECT bd.food_id, bd.item_qty as quantity, f.food_name as name, f.food_price as price,
                   f.food_src as image
            FROM billdetails bd
            JOIN food f ON bd.food_id = f.food_id
            WHERE bd.bill_id = ?
        `;
        
        db.query(itemsQuery, [orderId], (itemsErr, items) => {
            if (itemsErr) {
                console.log(itemsErr);
                result(itemsErr, null);
                return;
            }
            
            // Map the status code to a readable status
            let statusText = "Processing";
            if (order.status === 2) statusText = "Delivered";
            else if (order.status === 3) statusText = "Cancelled";
            
            // Format the order details for frontend
            const orderDetails = {
                id: order.bill_id.toString(),
                date: order.date,
                status: statusText,
                total: parseFloat(order.total) || 0,
                address: order.bill_address,
                phone: order.bill_phone,
                paymentMethod: order.bill_method,
                discount: parseFloat(order.bill_discount) || 0,
                deliveryFee: parseFloat(order.bill_delivery) || 0,
                paid: order.bill_paid === "paid",
                items: items.map(item => ({
                    id: item.food_id,
                    name: item.name,
                    quantity: item.quantity,
                    price: parseFloat(item.price) || 0,
                    image: item.image
                }))
            };
            
            result(null, orderDetails);
        });
    });
};
