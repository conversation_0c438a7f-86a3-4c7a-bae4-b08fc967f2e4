const { defineConfig } = require('@vue/cli-service')
const path = require('path');

module.exports = defineConfig({
  outputDir: path.resolve(__dirname, '../backend/restaurant_management'),
  devServer: {
    // Use a simpler configuration to properly handle WebSockets
    proxy: {
      '/api': {
        target: 'http://localhost:8001',
        changeOrigin: true
      }
    }
  },
  chainWebpack: config => {
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap(options => ({
        ...options,
        compilerOptions: {
          isCustomElement: tag => tag.startsWith('md-') || tag.startsWith('mwc-')
        }
      }));
  },
  // Configure runtimeCompiler to use the full build
  runtimeCompiler: true
})
