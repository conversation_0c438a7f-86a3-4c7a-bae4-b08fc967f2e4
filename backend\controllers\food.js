// import functions from Food model

import {
    getFoods,
    getFoodById,
    insertFood,
    updateFoodById,
    deleteFoodById,
    getFoodCategories,
    getFoodTypes,
    updateFoodAvailability
} from "../models/FoodModel.js";

// get all Foods
export const showFoods=(req,res)=>{
    getFoods((err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};


// get single Food
export const showFoodById=(req,res)=>{
    getFoodById(req.params.id,(err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};

// create Food
export const createFood=(req,res)=>{
    const data = req.body;
    insertFood(data,(err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};

// update Food
export const updateFood=(req,res)=>{
    const data = req.body;
    const id = req.params.id;
    updateFoodById(data,id,(err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};


// delete Food
export const deleteFood=(req,res)=>{
    const id = req.params.id;
    deleteFoodById(id,(err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};

// get all food categories
export const showFoodCategories = (req, res) => {
    getFoodCategories((err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving food categories",
                error: err
            });
        } else {
            res.json(results);
        }
    });
};

// get all food types
export const showFoodTypes = (req, res) => {
    getFoodTypes((err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving food types",
                error: err
            });
        } else {
            res.json(results);
        }
    });
};

// update food availability
export const updateAvailability = (req, res) => {
    const id = req.params.id;
    const { available } = req.body;
    
    if (available === undefined) {
        return res.status(400).json({
            message: "Available status is required"
        });
    }
    
    updateFoodAvailability(id, available, (err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error updating food availability",
                error: err
            });
        } else {
            res.json({
                message: "Food availability updated successfully",
                id: id,
                available: available
            });
        }
    });
};