// <PERSON><PERSON>t to create an admin account
import db from "./config/database.js";

// Admin user data
const adminUser = {
    user_name: "Admin",
    user_email: "<EMAIL>",
    user_password: "123456aA@",
    role: "admin",
    is_active: true
};

// Check if admin already exists
db.query(
    "SELECT user_id FROM user WHERE user_email = ?",
    [adminUser.user_email],
    (err, results) => {
        if (err) {
            console.error("Error checking for existing admin:", err);
            process.exit(1);
        }

        if (results.length > 0) {
            console.log("Admin account already exists with email:", adminUser.user_email);
            process.exit(0);
        }

        // Create admin user
        db.query(
            "INSERT INTO user SET ?",
            adminUser,
            (insertErr, insertResult) => {
                if (insertErr) {
                    console.error("Failed to create admin user:", insertErr);
                    process.exit(1);
                }

                console.log("Admin account created successfully!");
                console.log("Email:", adminUser.user_email);
                console.log("Password:", adminUser.user_password);
                console.log("User ID:", insertResult.insertId);
                process.exit(0);
            }
        );
    }
);
