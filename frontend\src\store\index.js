import {createStore} from "vuex"
import apiService from "../services/ApiService"

const store = createStore({
    state() {
        return {
            allFoods: [],
            user: undefined,
            admin: undefined,
            orders: [],
            notifications: [],
            useMockData: false,
            activePromotion: null,
            promotions: [
                { code: 'WELCOME10', discount: 0.1, description: '10% off your order' },
                { code: 'FREESHIP', discount: 5, description: 'Free shipping ($5 off)' },
                { code: 'SUMMER25', discount: 0.25, description: '25% off your order' }
            ]
        }
    },
    mutations: {
        setFoodsData(state, payload){
            state.allFoods = payload;
        },
        setUser(state, payload){
            state.user = payload;
        },
        setAdmin(state, payload){
            state.admin = payload;
        },

        setUseMockData(state, value) {
            state.useMockData = value;
        },
        addOrder(state, order) {
            state.orders.push(order);
        },
        updateOrder(state, updatedOrder) {
            const index = state.orders.findIndex(order => order.id === updatedOrder.id);
            if (index !== -1) {
                state.orders.splice(index, 1, updatedOrder);
            }
        },
        addNotification(state, notification) {
            state.notifications.push({
                id: Date.now(),
                ...notification,
                read: false,
                timestamp: new Date()
            });
        },
        markNotificationAsRead(state, notificationId) {
            const notification = state.notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.read = true;
            }
        },
        
        // Apply a promotion code to the cart
        applyPromotion(state, promoCode) {
            const promotion = state.promotions.find(p => p.code === promoCode);
            state.activePromotion = promotion || null;
            return promotion;
        },
        
        // Remove the currently applied promotion
        removePromotion(state) {
            state.activePromotion = null;
        }
    },
    actions: {
        
        // Get foods data from API
        async getFoodsData(context) {
            // If we're already using mock data, just load it directly
            if (context.state.useMockData) {
                context.dispatch('loadMockData');
                return;
            }
            
            try {
                // Get data using ApiService
                const foods = await apiService.getFoods();
                context.commit("setFoodsData", foods);
                console.log('Loaded food data from API:', foods.length, 'items');
            } catch (error) {
                console.log('API error, falling back to mock data:', error);
                context.commit('setUseMockData', true);
                context.dispatch('loadMockData');
            }
        },
        
        // Load mock data for development
        loadMockData({ commit }) {
            console.log('Loading mock data');
            const mockFoods = [
                {
                    id: 1,
                    name: 'Classic Beef Taco',
                    price: 3.99,
                    description: 'Traditional taco with seasoned ground beef, lettuce, tomato, and cheese',
                    image: 'taco-img.png',
                    category: 'tacos',
                    status: 'best seller',
                    discount: 0,
                    vote: 42,
                    star: 4.8
                },
                {
                    id: 2,
                    name: 'Chicken Burrito',
                    price: 7.99,
                    description: 'Large flour tortilla filled with grilled chicken, rice, beans, and cheese',
                    image: 'burrito-img.png',
                    category: 'burritos',
                    status: 'new',
                    discount: 1.00,
                    vote: 28,
                    star: 4.5
                },
                {
                    id: 3,
                    name: 'Loaded Nachos',
                    price: 8.99,
                    description: 'Crispy tortilla chips topped with melted cheese, jalapeños, guacamole, and sour cream',
                    image: 'nachos-img.png',
                    category: 'nachos',
                    status: '',
                    discount: 0,
                    vote: 36,
                    star: 4.7
                },
                {
                    id: 4,
                    name: 'Mexican Rice',
                    price: 2.99,
                    description: 'Fluffy rice cooked with tomatoes, onions, and Mexican spices',
                    image: 'salad-img.png',
                    category: 'sides',
                    status: '',
                    discount: 0,
                    vote: 18,
                    star: 4.2
                },
                {
                    id: 5,
                    name: 'Churros',
                    price: 4.99,
                    description: 'Fried dough pastry dusted with cinnamon sugar and served with chocolate sauce',
                    image: 'dessert-img.png',
                    category: 'desserts',
                    status: 'best seller',
                    discount: 0,
                    vote: 52,
                    star: 4.9
                },
                {
                    id: 6,
                    name: 'Mexican Coca-Cola',
                    price: 2.49,
                    description: 'Classic Coca-Cola made with real cane sugar',
                    image: 'coca-img.png',
                    category: 'drinks',
                    status: '',
                    discount: 0,
                    vote: 24,
                    star: 4.3
                }
            ];
            commit("setFoodsData", mockFoods);
        },
        
        // Send order to the server
        placeOrder({ commit, state }, order) {
            // Generate a new order ID
            const orderId = `ORDER-${Date.now()}`;
            
            // Create a formatted order object
            const newOrder = {
                id: orderId,
                items: order.items,
                totalPrice: order.totalPrice,
                status: 'Pending',
                timestamp: new Date(),
                customer: order.customer,
                promotion: state.activePromotion ? state.activePromotion.code : null
            };
            
            // Add to local state
            commit('addOrder', newOrder);
            commit('addNotification', {
                type: 'success',
                title: 'Order Placed',
                message: `Order #${orderId} has been placed successfully`
            });
            
            // Clear the applied promotion after order is placed
            commit('removePromotion');
            
            // Return the created order (for promise resolution)
            return Promise.resolve(newOrder);
        },
        
        // Apply a promotion code
        applyPromotion({ commit }, promoCode) {
            const result = commit('applyPromotion', promoCode);
            if (result) {
                commit('addNotification', {
                    type: 'success',
                    title: 'Promotion Applied',
                    message: `${result.description} has been applied to your order`
                });
                return Promise.resolve(result);
            } else {
                commit('addNotification', {
                    type: 'error',
                    title: 'Invalid Promotion',
                    message: `The promotion code "${promoCode}" is not valid`
                });
                return Promise.reject(new Error('Invalid promotion code'));
            }
        }
    }
})


export default store;