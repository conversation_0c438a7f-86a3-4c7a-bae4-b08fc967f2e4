<template>
  <div class="md-address-management-page">
    <div class="md-container">
      <!-- Page Header -->
      <div class="md-page-header">
        <h1 class="md-page-header__title">
          <i class="material-icons">location_on</i>
          Address Management
        </h1>
        <p class="md-page-header__subtitle">Manage your delivery addresses</p>
      </div>

      <!-- Address Manager Component -->
      <AddressManager />
    </div>
  </div>
</template>

<script>
import AddressManager from '../components/AddressManager.vue';

export default {
  name: 'AddressManagement',
  components: {
    AddressManager
  }
};
</script>

<style scoped>
.md-address-management-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 24px 0;
}

.md-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.md-page-header {
  text-align: center;
  margin-bottom: 32px;
}

.md-page-header__title {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.md-page-header__subtitle {
  margin: 0;
  color: #666;
  font-size: 16px;
}

@media (max-width: 768px) {
  .md-address-management-page {
    padding: 16px 0;
  }
  
  .md-page-header__title {
    font-size: 24px;
  }
}
</style>
