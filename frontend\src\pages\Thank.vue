<template>
    <div class="md-thank-you">
        <div class="md-section-header">
            <h1 class="md-section-header__title">
                <div class="md-thank-header">
                    <p>
                        <span>t</span>
                        <span>h</span>
                        <span>a</span>
                        <span>n</span>
                        <span>k</span>
                    </p>
                    <p>
                        <span>y</span>
                        <span>o</span>
                        <span>u</span>
                    </p>
                </div>
            </h1>
            <p class="md-section-header__subtitle">Your order has been placed successfully!</p>
        </div>

        <!-- Order Status Card -->
        <div class="md-thank-card">
            <div class="md-thank-card__icon-container">
                <i class="material-icons md-thank-card__icon">check_circle</i>
            </div>
            <h2 class="md-thank-card__title">Transaction Successful</h2>
            <p class="md-thank-card__text">
                Your order has been confirmed and is now being processed.
                You will receive an email confirmation shortly.
            </p>

            <!-- Order Shipping Status -->
            <div class="md-shipping-status">
                <h3 class="md-shipping-status__title">Order Status</h3>
                <div class="md-shipping-status__progress">
                    <div class="md-shipping-status__step md-shipping-status__step--active">
                        <div class="md-shipping-status__dot"></div>
                        <div class="md-shipping-status__label">Order Confirmed</div>
                    </div>
                    <div class="md-shipping-status__line"></div>
                    <div class="md-shipping-status__step">
                        <div class="md-shipping-status__dot"></div>
                        <div class="md-shipping-status__label">Preparing</div>
                    </div>
                    <div class="md-shipping-status__line"></div>
                    <div class="md-shipping-status__step">
                        <div class="md-shipping-status__dot"></div>
                        <div class="md-shipping-status__label">On the Way</div>
                    </div>
                    <div class="md-shipping-status__line"></div>
                    <div class="md-shipping-status__step">
                        <div class="md-shipping-status__dot"></div>
                        <div class="md-shipping-status__label">Delivered</div>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div v-if="orderDetails" class="md-order-summary">
                <h3 class="md-order-summary__title">Order #{{ orderId }}</h3>

                <div class="md-order-summary__info">
                    <div class="md-order-summary__row">
                        <span class="md-order-summary__label">Order Date:</span>
                        <span class="md-order-summary__value">{{ formatDate(orderDetails.bill_when) }}</span>
                    </div>
                    <div class="md-order-summary__row">
                        <span class="md-order-summary__label">Status:</span>
                        <span class="md-order-summary__value md-order-summary__status">{{ getStatusText(orderDetails.bill_status) }}</span>
                    </div>
                </div>

                <div class="md-order-summary__items">
                    <h4 class="md-order-summary__items-title">Order Items</h4>
                    <div class="md-order-summary__items-list">
                        <div v-for="item in orderItems" :key="item.food_id" class="md-order-summary__item">
                            <div class="md-order-summary__item-info">
                                <span class="md-order-summary__item-qty">{{ item.item_qty }}</span>
                                <span class="md-order-summary__item-name">{{ item.food_title || item.food_name || 'Unknown Item' }}</span>
                            </div>
                            <div class="md-order-summary__item-price">
                                <span>${{ ((parseFloat(item.price || item.food_price || 0) * parseInt(item.item_qty || 1)).toFixed(2)) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="md-order-summary__total">
                    <div class="md-order-summary__row">
                        <span class="md-order-summary__label">Discount:</span>
                        <span class="md-order-summary__value">-${{ parseFloat(orderDetails.bill_discount || 0).toFixed(2) }}</span>
                    </div>
                    <div class="md-order-summary__row">
                        <span class="md-order-summary__label">Delivery:</span>
                        <span class="md-order-summary__value">${{ parseFloat(orderDetails.bill_delivery || 0).toFixed(2) }}</span>
                    </div>
                    <div class="md-order-summary__row md-order-summary__row--total">
                        <span class="md-order-summary__label">Total:</span>
                        <span class="md-order-summary__value">${{ parseFloat(orderDetails.bill_total || 0).toFixed(2) }}</span>
                    </div>
                </div>
            </div>

            <div class="md-thank-card__actions">
                <router-link to="/profile" class="md-button md-button--primary">
                    <i class="material-icons">receipt_long</i>
                    View My Orders
                </router-link>
                <router-link to="/menu" class="md-button md-button--outline">
                    <i class="material-icons">restaurant_menu</i>
                    Continue Shopping
                </router-link>
                <router-link to="/profile?tab=current_orders" class="md-button md-button--outline">
                    <i class="material-icons">track_changes</i>
                    Track Order
                </router-link>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';

export default {
    name: 'Thank',
    data() {
        return {
            orderId: null,
            orderDetails: null,
            orderItems: [],
            isLoading: true,
            error: null
        };
    },
    async created() {
        // Get order ID from query params
        this.orderId = this.$route.query.order_id;

        if (this.orderId) {
            await this.loadOrderDetails();
        } else {
            console.warn('No order ID provided in query params');
            this.isLoading = false;
        }
    },
    methods: {
        async loadOrderDetails() {
            try {
                this.isLoading = true;

                // Load order status
                const statusResponse = await axios.get(`/api/billstatus/bill/${this.orderId}`);
                if (statusResponse.data && statusResponse.data.length > 0) {
                    this.orderDetails = statusResponse.data[0];
                }

                // Load order items
                const itemsResponse = await axios.get(`/api/billdetails/${this.orderId}`);
                if (itemsResponse.data) {
                    this.orderItems = itemsResponse.data;
                }

                console.log('Order details loaded:', this.orderDetails);
                console.log('Order items loaded:', this.orderItems);

            } catch (error) {
                console.error('Error loading order details:', error);
                this.error = 'Failed to load order details';
            } finally {
                this.isLoading = false;
            }
        },

        formatDate(dateString) {
            if (!dateString) return 'N/A';

            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            } catch (error) {
                return dateString;
            }
        },

        getStatusText(status) {
            const statusMap = {
                1: 'Processing',
                2: 'Confirmed',
                3: 'Preparing',
                4: 'On the Way',
                5: 'Delivered'
            };
            return statusMap[status] || 'Unknown';
        }
    }
}
</script>
<script setup>
// No custom directives needed for this component
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css?family=Luckiest+Guy");

.md-thank-you {
  padding: var(--md-sys-spacing-large);
  max-width: 800px;
  margin: 0 auto;
}

.md-section-header {
  text-align: center;
  margin-bottom: var(--md-sys-spacing-large);
}

.md-section-header__title {
  margin: 0 0 var(--md-sys-spacing-small);
  font-family: var(--md-sys-typescale-headline-large-font);
  font-size: var(--md-sys-typescale-headline-large-size);
  color: var(--md-sys-color-on-surface);
}

.md-section-header__subtitle {
  margin: 0;
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-on-surface-variant);
}

/* Thank You Animation */
.md-thank-header {
  width: 100%;
  height: 120px;
  margin: auto;
  display: block;
  text-align: center;
}

.md-thank-header p {
  margin: 0;
}

.md-thank-header p span {
  position: relative;
  color: var(--md-sys-color-primary) !important;
  top: 20px;
  display: inline-block;
  animation: bounce 0.3s ease infinite alternate;
  font-size: 60px;
  font-family: 'Luckiest Guy', cursive;
  text-shadow: 0 1px 0 var(--md-sys-color-primary-container),
               0 2px 0 var(--md-sys-color-primary-container),
               0 3px 0 var(--md-sys-color-primary-container),
               0 4px 0 var(--md-sys-color-primary-container),
               0 5px 0 var(--md-sys-color-primary-container),
               0 6px 0 transparent,
               0 7px 0 transparent,
               0 8px 0 transparent,
               0 9px 0 transparent,
               0 10px 10px rgba(0, 0, 0, 0.2);
}

.md-thank-header p span:nth-child(2) {
  animation-delay: 0.1s;
}

.md-thank-header p span:nth-child(3) {
  animation-delay: 0.2s;
}

.md-thank-header p span:nth-child(4) {
  animation-delay: 0.3s;
}

.md-thank-header p span:nth-child(5) {
  animation-delay: 0.4s;
}

/* Thank You Card */
.md-thank-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-large);
  box-shadow: var(--md-elevation-level2);
  text-align: center;
  margin-top: var(--md-sys-spacing-large);
}

.md-thank-card__icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-thank-card__icon {
  font-size: 64px;
  color: var(--md-sys-color-primary);
}

.md-thank-card__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-medium);
}

.md-thank-card__text {
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0 0 var(--md-sys-spacing-large);
}

/* Shipping Status */
.md-shipping-status {
  margin: var(--md-sys-spacing-large) 0;
  padding: var(--md-sys-spacing-large);
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
}

.md-shipping-status__title {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-large);
  text-align: center;
}

.md-shipping-status__progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.md-shipping-status__step {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.md-shipping-status__dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--md-sys-color-surface);
  border: 2px solid var(--md-sys-color-outline);
  margin-bottom: var(--md-sys-spacing-small);
}

.md-shipping-status__step--active .md-shipping-status__dot {
  background-color: var(--md-sys-color-primary);
  border-color: var(--md-sys-color-primary);
}

.md-shipping-status__label {
  font-family: var(--md-sys-typescale-body-small-font);
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  text-align: center;
  width: 80px;
}

.md-shipping-status__step--active .md-shipping-status__label {
  color: var(--md-sys-color-primary);
  font-weight: 500;
}

.md-shipping-status__line {
  flex: 1;
  height: 2px;
  background-color: var(--md-sys-color-outline-variant);
  z-index: 1;
}

/* Button Actions */
.md-thank-card__actions {
  display: flex;
  justify-content: center;
  gap: var(--md-sys-spacing-medium);
  margin-top: var(--md-sys-spacing-large);
  flex-wrap: wrap;
}

.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border-radius: var(--md-sys-shape-corner-full);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: 500;
  text-transform: uppercase;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border: none;
  box-shadow: var(--md-elevation-level1);
}

.md-button--primary:hover {
  background-color: var(--md-sys-color-primary-hover);
  box-shadow: var(--md-elevation-level2);
}

.md-button--outline {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-button--outline:hover {
  background-color: var(--md-sys-color-surface-variant);
}

@keyframes bounce {
  100% {
    top: -20px;
    text-shadow: 0 1px 0 var(--md-sys-color-primary-container),
                0 2px 0 var(--md-sys-color-primary-container),
                0 3px 0 var(--md-sys-color-primary-container),
                0 4px 0 var(--md-sys-color-primary-container),
                0 5px 0 var(--md-sys-color-primary-container),
                0 6px 0 var(--md-sys-color-primary-container),
                0 7px 0 var(--md-sys-color-primary-container),
                0 8px 0 var(--md-sys-color-primary-container),
                0 9px 0 var(--md-sys-color-primary-container),
                0 50px 25px rgba(0, 0, 0, 0.2);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .md-thank-you {
    padding: var(--md-sys-spacing-medium);
  }

  .md-thank-header {
    height: 80px;
  }

  .md-thank-header p span {
    font-size: 40px;
  }

  .md-thank-card__actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--md-sys-spacing-small);
  }

  .md-shipping-status__progress {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--md-sys-spacing-medium) var(--md-sys-spacing-large);
  }

  .md-shipping-status__line {
    display: none;
  }

  .md-shipping-status__step {
    flex: 0 0 40%;
    margin-bottom: var(--md-sys-spacing-medium);
  }
}

/* Order Summary Styles */
.md-order-summary {
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: var(--md-sys-spacing-medium);
  margin-top: var(--md-sys-spacing-large);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.md-order-summary__title {
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-medium) 0;
  text-align: center;
}

.md-order-summary__info {
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-order-summary__row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-small);
}

.md-order-summary__row--total {
  border-top: 1px solid var(--md-sys-color-outline-variant);
  padding-top: var(--md-sys-spacing-small);
  margin-top: var(--md-sys-spacing-small);
  font-weight: 600;
}

.md-order-summary__label {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
}

.md-order-summary__value {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.md-order-summary__status {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border-radius: var(--md-sys-shape-corner-small);
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: 600;
}

.md-order-summary__items {
  margin: var(--md-sys-spacing-medium) 0;
}

.md-order-summary__items-title {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-small) 0;
}

.md-order-summary__items-list {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-small);
  padding: var(--md-sys-spacing-small);
}

.md-order-summary__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--md-sys-spacing-small) 0;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-order-summary__item:last-child {
  border-bottom: none;
}

.md-order-summary__item-info {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
}

.md-order-summary__item-qty {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  padding: 2px 8px;
  border-radius: var(--md-sys-shape-corner-small);
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.md-order-summary__item-name {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
}

.md-order-summary__item-price {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
  font-weight: 600;
}

.md-order-summary__total {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-small);
  padding: var(--md-sys-spacing-medium);
  margin-top: var(--md-sys-spacing-medium);
}
</style>