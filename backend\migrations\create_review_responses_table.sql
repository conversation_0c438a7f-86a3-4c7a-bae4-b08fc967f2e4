-- Create review_responses table for admin responses to reviews
CREATE TABLE IF NOT EXISTS `review_responses` (
  `response_id` INT AUTO_INCREMENT PRIMARY KEY,
  `review_id` INT NOT NULL,
  `response` TEXT NOT NULL,
  `admin_id` INT,
  `response_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREI<PERSON><PERSON> KEY (`review_id`) REFERENCES `reviews`(`review_id`) ON DELETE CASCADE,
  FOREIGN KEY (`admin_id`) REFERENCES `user`(`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Add additional columns to reviews table for better management
ALTER TABLE `reviews` 
ADD COLUMN IF NOT EXISTS `hidden` TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS `admin_notes` TEXT;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS `idx_reviews_food_rating` ON `reviews` (`food_id`, `rating`);
CREATE INDEX IF NOT EXISTS `idx_reviews_user_created` ON `reviews` (`user_id`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_reviews_reported` ON `reviews` (`reported`);
CREATE INDEX IF NOT EXISTS `idx_reviews_hidden` ON `reviews` (`hidden`);

-- Create trigger to update food average rating when review is added/updated/deleted
DELIMITER //

DROP TRIGGER IF EXISTS `update_food_rating_after_insert`//
CREATE TRIGGER `update_food_rating_after_insert`
AFTER INSERT ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `food` 
    SET `food_star` = (
        SELECT ROUND(AVG(rating), 1) 
        FROM `reviews` 
        WHERE `food_id` = NEW.food_id AND `hidden` = 0
    ),
    `food_vote` = (
        SELECT COUNT(*) 
        FROM `reviews` 
        WHERE `food_id` = NEW.food_id AND `hidden` = 0
    )
    WHERE `food_id` = NEW.food_id;
END//

DROP TRIGGER IF EXISTS `update_food_rating_after_update`//
CREATE TRIGGER `update_food_rating_after_update`
AFTER UPDATE ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `food` 
    SET `food_star` = (
        SELECT ROUND(AVG(rating), 1) 
        FROM `reviews` 
        WHERE `food_id` = NEW.food_id AND `hidden` = 0
    ),
    `food_vote` = (
        SELECT COUNT(*) 
        FROM `reviews` 
        WHERE `food_id` = NEW.food_id AND `hidden` = 0
    )
    WHERE `food_id` = NEW.food_id;
END//

DROP TRIGGER IF EXISTS `update_food_rating_after_delete`//
CREATE TRIGGER `update_food_rating_after_delete`
AFTER DELETE ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `food` 
    SET `food_star` = (
        SELECT COALESCE(ROUND(AVG(rating), 1), 0) 
        FROM `reviews` 
        WHERE `food_id` = OLD.food_id AND `hidden` = 0
    ),
    `food_vote` = (
        SELECT COUNT(*) 
        FROM `reviews` 
        WHERE `food_id` = OLD.food_id AND `hidden` = 0
    )
    WHERE `food_id` = OLD.food_id;
END//

DELIMITER ;
