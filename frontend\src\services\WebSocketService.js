/**
 * Mock WebSocket Service - WebSocket functionality is completely disabled
 * This is a stub implementation that only provides mock data
 */

class WebSocketService {
  constructor() {
    this.isConnected = false;
    this.listeners = {};
    console.log('MOCK WebSocket Service initialized - WebSocket is completely disabled');
  }

  /**
   * Mock connect method - does nothing except trigger fallback to mock data
   */
  connect() {
    console.log('WebSocket is completely disabled - using mock data');
    
    // Immediately trigger fallback to mock data
    setTimeout(() => {
      this._triggerEvent('fallback_to_mock', { reason: 'websocket_disabled' });
    }, 0);
    
    return false;
  }
  
  /**
   * Mock send method - does nothing
   * @param {object} data - Ignored parameter
   * @returns {boolean} - Always false
   */
  send(data) {
    console.log('WebSocket is disabled, cannot send message:', data);
    return false;
  }
  
  /**
   * Mock disconnect method - does nothing
   */
  disconnect() {
    // No-op since WebSocket is disabled
  }
  
  /**
   * Register an event listener
   * @param {string} event - The event name
   * @param {function} callback - The callback function
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    
    this.listeners[event].push(callback);
  }
  
  /**
   * Remove an event listener
   * @param {string} event - The event name
   * @param {function} callback - The callback function to remove
   */
  off(event, callback) {
    if (!this.listeners[event]) return;
    
    this.listeners[event] = this.listeners[event].filter(
      (cb) => cb !== callback
    );
  }
  

  
  /**
   * Trigger an event for all registered listeners
   * @param {string} event - The event name
   * @param {*} data - The event data
   * @private
   */
  _triggerEvent(event, data) {
    if (!this.listeners[event]) return;
    
    for (const callback of this.listeners[event]) {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} event handler:`, error);
      }
    }
  }
}

// Create a singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
