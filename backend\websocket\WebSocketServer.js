// MOCK WebSocket server - No actual WebSocket functionality
// WebSocket has been completely disabled due to persistent connection issues

/**
 * Mock WebSocket service that doesn't use any actual WebSocket functionality
 * This is a complete replacement that doesn't attempt any WebSocket connections
 */
class WebSocketService {
  constructor() {
    console.log('MOCK WebSocket server initialized - No actual WebSocket functionality');
  }

  /**
   * Mock initialize method - does nothing
   * @param {object} server - Ignored parameter
   * @param {string} path - Ignored parameter
   */
  initialize(server, path = '/ws') {
    console.log('WebSocket functionality is completely disabled');
    console.log('Using mock data instead of WebSocket communication');
    return true;
  }
  
  /**
   * Mock broadcast method - does nothing
   * @param {object} data - Ignored parameter
   */
  broadcast(data) {
    console.log('Mock broadcast called (WebSocket disabled):', data);
  }

  /**
   * Mock sendToClient method - does nothing
   * @param {object} client - Ignored parameter
   * @param {object} data - Ignored parameter
   */
  sendToClient(client, data) {
    console.log('Mock sendToClient called (WebSocket disabled):', data);
  }

  /**
   * Get mock menu data for testing
   * @returns {Array} - Array of menu items
   */
  getMockMenuData() {
    return [
      {
        id: 1,
        name: 'Classic Beef Taco',
        price: 3.99,
        description: 'Traditional taco with seasoned ground beef, lettuce, tomato, and cheese',
        image: 'taco-img.png',
        category: 'tacos',
        status: 'best seller',
        discount: 0,
        vote: 42,
        star: 4.8
      },
      {
        id: 2,
        name: 'Chicken Burrito',
        price: 7.99,
        description: 'Large flour tortilla filled with grilled chicken, rice, beans, and cheese',
        image: 'burrito-img.png',
        category: 'burritos',
        status: 'new',
        discount: 1.00,
        vote: 28,
        star: 4.5
      },
      {
        id: 3,
        name: 'Loaded Nachos',
        price: 8.99,
        description: 'Crispy tortilla chips topped with melted cheese, jalapeños, guacamole, and sour cream',
        image: 'nachos-img.png',
        category: 'nachos',
        status: '',
        discount: 0,
        vote: 36,
        star: 4.7
      },
      {
        id: 4,
        name: 'Mexican Rice',
        price: 2.99,
        description: 'Fluffy rice cooked with tomatoes, onions, and Mexican spices',
        image: 'salad-img.png',
        category: 'sides',
        status: '',
        discount: 0,
        vote: 18,
        star: 4.2
      },
      {
        id: 5,
        name: 'Guacamole',
        price: 3.99,
        description: 'Fresh avocados mashed with lime juice, cilantro, and diced tomatoes',
        image: 'guacamole-img.png',
        category: 'sides',
        status: '',
        discount: 0,
        vote: 24,
        star: 4.6
      },
      {
        id: 6,
        name: 'Mexican Coca-Cola',
        price: 2.49,
        description: 'Classic Coca-Cola made with real cane sugar',
        image: 'coca-img.png',
        category: 'drinks',
        status: '',
        discount: 0,
        vote: 24,
        star: 4.3
      }
    ];
  }
}

// Create a singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
