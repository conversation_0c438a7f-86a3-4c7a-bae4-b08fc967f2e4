// Import connection
import db from "../config/database.js";

// Function to check and update the food table schema
export const setupFoodTable = () => {
  // Check if food_description column exists, add it if not
  db.query(
    `SELECT COUNT(*) AS column_exists 
     FROM information_schema.COLUMNS 
     WHERE TABLE_SCHEMA = 'db_restaurant' 
     AND TABLE_NAME = 'food' 
     AND COLUMN_NAME = 'food_description'`, 
    (err, results) => {
      if (err) {
        console.error("Error checking food_description column:", err);
        return;
      }
      
      // If column doesn't exist, add it
      if (results[0].column_exists === 0) {
        db.query(
          "ALTER TABLE food ADD COLUMN food_description TEXT DEFAULT NULL",
          (err) => {
            if (err) {
              console.error("Error adding food_description column:", err);
            } else {
              console.log("Added food_description column to food table");
            }
          }
        );
      }
    }
  );

  // Check if food_image column exists, add it if not
  db.query(
    `SELECT COUNT(*) AS column_exists 
     FROM information_schema.COLUMNS 
     WHERE TABLE_SCHEMA = 'db_restaurant' 
     AND TABLE_NAME = 'food' 
     AND COLUMN_NAME = 'food_image'`, 
    (err, results) => {
      if (err) {
        console.error("Error checking food_image column:", err);
        return;
      }
      
      // If column doesn't exist, add it
      if (results[0].column_exists === 0) {
        db.query(
          "ALTER TABLE food ADD COLUMN food_image VARCHAR(255) DEFAULT NULL",
          (err) => {
            if (err) {
              console.error("Error adding food_image column:", err);
            } else {
              console.log("Added food_image column to food table");
            }
          }
        );
      }
    }
  );
};
