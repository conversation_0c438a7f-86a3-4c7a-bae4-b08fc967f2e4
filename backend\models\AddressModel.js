// import database connection
import db from "../config/database.js";

// Get all addresses for a user
export const getUserAddresses = (userId, result) => {
    db.query(
        `SELECT * FROM user_address
         WHERE user_id = ?
         ORDER BY is_default DESC, created_at DESC`,
        [userId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Get default address for a user
export const getUserDefaultAddress = (userId, result) => {
    db.query(
        `SELECT * FROM user_address
         WHERE user_id = ? AND is_default = 1
         LIMIT 1`,
        [userId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results.length > 0 ? results[0] : null);
            }
        }
    );
};

// Get address by ID
export const getAddressById = (addressId, result) => {
    db.query(
        "SELECT * FROM user_address WHERE address_id = ?",
        [addressId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results.length > 0 ? results[0] : null);
            }
        }
    );
};

// Create new address
export const createAddress = (addressData, result) => {
    const {
        user_id,
        address_label,
        recipient_name,
        recipient_phone,
        address_line1,
        address_line2,
        district,
        city,
        postal_code,
        country,
        is_default
    } = addressData;

    db.query(
        `INSERT INTO user_address
         (user_id, address_label, recipient_name, recipient_phone,
          address_line1, address_line2, district, city, postal_code, country, is_default)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [user_id, address_label, recipient_name, recipient_phone,
         address_line1, address_line2, district, city, postal_code, country, is_default || 0],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, { address_id: results.insertId, ...addressData });
            }
        }
    );
};

// Update address
export const updateAddress = (addressId, addressData, result) => {
    const {
        address_label,
        recipient_name,
        recipient_phone,
        address_line1,
        address_line2,
        district,
        city,
        postal_code,
        country,
        is_default
    } = addressData;

    db.query(
        `UPDATE user_address SET
         address_label = ?, recipient_name = ?, recipient_phone = ?,
         address_line1 = ?, address_line2 = ?, district = ?, city = ?,
         postal_code = ?, country = ?, is_default = ?
         WHERE address_id = ?`,
        [address_label, recipient_name, recipient_phone,
         address_line1, address_line2, district, city, postal_code, country, is_default || 0, addressId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Delete address
export const deleteAddress = (addressId, result) => {
    db.query(
        "DELETE FROM user_address WHERE address_id = ?",
        [addressId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Set address as default
export const setDefaultAddress = (addressId, userId, result) => {
    // First, unset all default addresses for this user
    db.query(
        "UPDATE user_address SET is_default = 0 WHERE user_id = ?",
        [userId],
        (err) => {
            if (err) {
                console.log(err);
                result(err, null);
                return;
            }

            // Then set the specified address as default
            db.query(
                "UPDATE user_address SET is_default = 1 WHERE address_id = ? AND user_id = ?",
                [addressId, userId],
                (err, results) => {
                    if (err) {
                        console.log(err);
                        result(err, null);
                    } else {
                        result(null, results);
                    }
                }
            );
        }
    );
};

// Check if user owns address
export const checkAddressOwnership = (addressId, userId, result) => {
    db.query(
        "SELECT address_id FROM user_address WHERE address_id = ? AND user_id = ?",
        [addressId, userId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results.length > 0);
            }
        }
    );
};

// Get address count for user
export const getUserAddressCount = (userId, result) => {
    db.query(
        "SELECT COUNT(*) as count FROM user_address WHERE user_id = ?",
        [userId],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results[0].count);
            }
        }
    );
};
