<template>
  <div class="md-dialog-overlay" @click.self="$emit('close')">
    <div v-if="user" class="md-dialog">
      <div class="md-dialog__header">
        <h2 class="md-dialog__title">Quick View</h2>
        <slot></slot>
      </div>

      <div class="md-dialog__content">
        <div class="md-product">
          <div class="md-product__image">
            <template v-if="getImageSrc(matchFood)">
              <img :src="getImageSrc(matchFood)" :alt="matchFood.food_name" @error="handleImageError">
            </template>
            <div v-else class="md-product__placeholder">
              <span class="material-icons">image</span>
              <span>Image loading...</span>
            </div>
          </div>
          <div class="md-product__content">
            <h3 class="md-product__title" v-if="matchFood">{{ matchFood.food_name }}</h3>
            <h3 class="md-product__title" v-else>Loading...</h3>

            <div class="md-rating" v-if="matchFood">
              <div class="md-rating__stars">
                <span
                  class="material-icons"
                  v-for="n in 5"
                  :key="n"
                  :class="{'md-rating__star--filled': n <= Math.floor(parseFloat(matchFood.food_star))}"
                >star</span>
              </div>
              <span class="md-rating__count">({{ matchFood.food_vote }} reviews)</span>
            </div>

            <p class="md-product__description" v-if="matchFood">{{ matchFood.food_desc }}</p>

            <div class="md-product__price" v-if="matchFood">
              <span class="md-product__current-price">
                ${{ parseFloat(matchFood.food_price) - parseFloat(matchFood.food_discount) }}
              </span>
              <span v-if="parseFloat(matchFood.food_discount) > 0" class="md-product__original-price">
                ${{ parseFloat(matchFood.food_price) }}
              </span>
            </div>

            <div class="md-product__options">
              <div class="md-quantity-field">
                <label>
                  <span class="md-quantity-field__label">Quantity</span>
                  <div class="md-quantity-field__controls">
                    <button
                      class="md-button md-button--icon"
                      @click="qty > 1 && qty--"
                    >
                      <span class="material-icons">remove</span>
                    </button>
                    <input
                      type="number"
                      v-model="qty"
                      min="1"
                      class="md-quantity-field__input"
                      @change="onQtyChange($event)"
                    >
                    <button
                      class="md-button md-button--icon"
                      @click="qty++"
                    >
                      <span class="material-icons">add</span>
                    </button>
                  </div>
                </label>
              </div>
            </div>

            <button class="md-button md-button--primary md-product__action" @click="addToCart">
              <span class="material-icons">add_shopping_cart</span>
              Add to cart
            </button>
          </div>
        </div>

        <!-- Reviews Section -->
        <reviews-section
          v-if="matchFood && matchFood.food_id"
          :food-id="matchFood.food_id"
          @notification="showNotification"
        ></reviews-section>

        <!-- Notification Banner -->
        <div
          v-if="notification.show"
          class="md-notification"
          :class="`md-notification--${notification.type}`"
        >
          <span class="material-icons md-notification__icon">
            {{ notificationIcon }}
          </span>
          <span class="md-notification__message">{{ notification.message }}</span>
          <button class="md-notification__close" @click="notification.show = false">
            <span class="material-icons">close</span>
          </button>
        </div>
      </div>
    </div>

    <div v-else class="md-dialog md-dialog--small">
      <div class="md-dialog__header">
        <h2 class="md-dialog__title">Login Required</h2>
        <slot></slot>
      </div>
      <div class="md-dialog__content md-dialog__content--center">
        <span class="material-icons md-dialog__icon">account_circle</span>
        <p class="md-dialog__message">Please login to continue</p>
        <router-link to="/login" class="md-button md-button--primary">
          Login now
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { mapState } from "vuex";
import ReviewsSection from "./ReviewsSection.vue";

export default {
    props: ['food'],
    name: "QuickView",
    components: {
      ReviewsSection
    },

    data() {
        return {
            qty: 1,
            matchFood: {
                food_id: null,
                food_name: 'Loading...',
                food_desc: 'Loading product information...',
                food_price: '0',
                food_discount: '0',
                food_star: '0',
                food_vote: '0'
            },
            notification: {
              show: false,
              type: 'success',
              message: ''
            }
        }
    },

    computed: {
        ...mapState(["user"]),

        notificationIcon() {
          const icons = {
            success: 'check_circle',
            error: 'error',
            warning: 'warning',
            info: 'info'
          };

          return icons[this.notification.type] || 'info';
        }
    },

    async created() {
        try {
            // Initialize food_id from the prop to ensure it's not null
            this.matchFood.food_id = this.food;

            // Use the correct API endpoint with /api prefix
            let data = await axios.get('/api/foods/' + this.food);
            this.matchFood = data.data;

            // Double-check that food_id is set
            if (!this.matchFood.food_id) {
                this.matchFood.food_id = this.food;
            }

            console.log('Food data loaded:', this.matchFood);
        } catch (error) {
            console.error('Error fetching food details:', error);
            // Keep the default matchFood in case of error
            // But ensure the food_id is set from the prop
            this.matchFood.food_id = this.food;
        }
    },

    methods: {
        onQtyChange(event) {
            if (event.target.value < 1) {
                this.qty = 1;
            }
        },

        async addToCart() {
            if (this.user) {
                try {
                    // Make sure we have a valid food_id, either from matchFood or the original prop
                    const foodId = this.matchFood.food_id || this.food;

                    if (!foodId) {
                        console.error('Food ID is missing, cannot add to cart');
                        return;
                    }

                    let data = {
                        user_id: this.user.user_id,
                        food_id: parseInt(foodId),
                        item_qty: this.qty
                    }

                    console.log('Adding to cart:', data);

                    // First check if item exists in cart
                    try {
                        const existingItem = await axios.get(`/api/cartItem/${this.user.user_id}/${foodId}`);
                        if (existingItem.data && existingItem.data.length > 0) {
                            // Update quantity if already in cart
                            data.item_qty = parseInt(existingItem.data[0].item_qty) + this.qty;
                            await axios.put("/api/cartItem/", data);
                        } else {
                            // Add new item if not in cart
                            await axios.post("/api/cartItem", data);
                        }
                        this.$emit('close');
                        // Show success notification if needed
                    } catch (error) {
                        console.log('Item not found in cart, adding new item');
                        // Item doesn't exist in cart, add it
                        await axios.post("/api/cartItem", data);
                        this.$emit('close');
                    }
                } catch (error) {
                    console.error('Error adding item to cart:', error);
                    // Show error notification if needed
                }
            }
        },

        showNotification({ type, message }) {
          this.notification = {
            show: true,
            type: type || 'info',
            message
          };

          // Auto-dismiss after 5 seconds
          setTimeout(() => {
            this.notification.show = false;
          }, 5000);
        },

        // Helper method to get image source with fallbacks
        getImageSrc(food) {
            if (!food) {
                return require('../assets/images/nachos-img.png');
            }

            // Handle image path formats from both API and mock data
            const imageName = food.food_image || food.food_src || food.image;

            // If it's a full URL, use it directly
            if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
                return imageName;
            }

            // Handle cases where database returns 'food-img.png' which doesn't exist
            if (imageName === 'food-img.png') {
                // Get category-based default instead
                const category = (food.food_category || food.category || 'other').toLowerCase();
                return this.getCategoryDefaultImage(category);
            }

            // Use a safer approach with a default fallback image
            if (imageName) {
                try {
                    return require(`../assets/images/${imageName}`);
                } catch (error) {
                    // Image not found, will select category-based default
                    console.log('Image not found:', imageName);
                }
            }

            // Select a default image based on category
            const category = (food.food_category || food.category || 'other').toLowerCase();
            return this.getCategoryDefaultImage(category);
        },

        // Helper method to get default images by category
        getCategoryDefaultImage(category) {
            // These are guaranteed to exist in our assets
            if (category.includes('taco')) {
                return require('../assets/images/taco-img.png');
            } else if (category.includes('burrito')) {
                return require('../assets/images/burrito-img.png');
            } else if (category.includes('nacho')) {
                return require('../assets/images/nachos-img.png');
            } else if (category.includes('side') || category.includes('salad')) {
                return require('../assets/images/salad-img.png');
            } else if (category.includes('dessert')) {
                return require('../assets/images/dessert-img.png');
            } else if (category.includes('coca') || category.includes('drink')) {
                return require('../assets/images/coca-img.png');
            }

            // Default fallback image for any other category
            return require('../assets/images/nachos-img.png');
        },

        // Handle image loading errors
        handleImageError(event) {
            console.warn('Image failed to load, using fallback');
            event.target.src = require('../assets/images/nachos-img.png');
        }
    }
}
</script>

<style scoped>
.md-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--md-sys-spacing-medium);
  animation: fadeIn 0.2s ease;
}

.md-dialog {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.md-dialog--small {
  max-width: 400px;
}

.md-dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--md-sys-spacing-medium);
  border-bottom: 1px solid var(--md-sys-color-outline);
}

.md-dialog__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-dialog__content {
  padding: var(--md-sys-spacing-medium);
  overflow-y: auto;
}

.md-dialog__content--center {
  text-align: center;
  padding: var(--md-sys-spacing-large);
}

.md-dialog__icon {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-dialog__message {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: var(--md-sys-spacing-large);
}

.md-product {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-large);
}

.md-product__image {
  aspect-ratio: 16/9;
  border-radius: var(--md-sys-shape-corner-medium);
  overflow: hidden;
}

.md-product__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-product__content {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-product__title {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-product__description {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.md-product__price {
  display: flex;
  align-items: baseline;
  gap: var(--md-sys-spacing-small);
}

.md-product__current-price {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-primary);
}

.md-product__original-price {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: line-through;
}

.md-product__options {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-quantity-field {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-small);
}

.md-quantity-field__label {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
}

.md-quantity-field__controls {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface-variant);
  padding: var(--md-sys-spacing-small);
}

.md-quantity-field__input {
  width: 60px;
  text-align: center;
  border: none;
  background: none;
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
  -moz-appearance: textfield;
  appearance: textfield;
}

.md-quantity-field__input::-webkit-outer-spin-button,
.md-quantity-field__input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.md-quantity-field__input::-moz-outer-spin-button,
.md-quantity-field__input::-moz-inner-spin-button {
  appearance: none;
  margin: 0;
}

.md-button--icon {
  width: 36px;
  height: 36px;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  display: flex;
  align-items: center;
  justify-content: center;
}

.md-product__action {
  margin-top: var(--md-sys-spacing-medium);
}

.md-product__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: var(--md-sys-spacing-large);
  gap: var(--md-sys-spacing-medium);
}

.md-product__placeholder .material-icons {
  font-size: 48px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Desktop styles */
@media (min-width: 769px) {
  .md-product {
    flex-direction: row;
  }

  .md-product__image {
    flex: 0 0 50%;
  }

  .md-product__content {
    flex: 1;
  }
}

/* Add these new styles for notification */
.md-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  max-width: 400px;
  z-index: 1000;
  animation: slide-in 0.3s ease;
}

@keyframes slide-in {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.md-notification--success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.md-notification--error {
  background-color: #ffebee;
  color: #c62828;
}

.md-notification--warning {
  background-color: #fff8e1;
  color: #ff8f00;
}

.md-notification--info {
  background-color: #e3f2fd;
  color: #1565c0;
}

.md-notification__icon {
  margin-right: 12px;
}

.md-notification__message {
  flex: 1;
}

.md-notification__close {
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.md-notification__close:hover {
  opacity: 1;
}
</style>
