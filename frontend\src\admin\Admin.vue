<template>
    <div class="md-admin-page">
        <div class="md-admin-container">
            <div class="md-card md-admin-card">
                <div class="md-admin-card__header">
                    <i class="material-icons md-admin-card__icon">admin_panel_settings</i>
                    <h2 class="md-admin-card__title">Administrator Login</h2>
                </div>

                <div v-if="errors.length" class="md-admin-card__error-box">
                    <div class="md-admin-card__error-box-icon">
                        <i class="material-icons">error</i>
                    </div>
                    <ul class="md-admin-card__error-list">
                        <li v-for="error in errors" :key="error" class="md-admin-card__error-item">
                            {{ error }}
                        </li>
                    </ul>
                </div>

                <form id="adminForm" @submit="handleSubmit" novalidate autocomplete="off" class="md-admin-form">
                    <div class="md-form-field">
                        <div class="md-form-field__input-container">
                            <i class="material-icons md-form-field__icon">person</i>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                class="md-form-field__input"
                                placeholder="Username" 
                                v-model="adminObj.username" />
                        </div>
                    </div>

                    <div class="md-form-field">
                        <div class="md-form-field__input-container">
                            <i class="material-icons md-form-field__icon">lock</i>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="md-form-field__input"
                                placeholder="Password" 
                                v-model="adminObj.password" />
                        </div>
                    </div>

                    <div class="md-form-field">
                        <button type="submit" class="md-button md-button--primary" :disabled="isLoading">
                            <i class="material-icons" v-if="!isLoading">login</i>
                            <i class="material-icons spin" v-else>sync</i>
                            <span>{{ isLoading ? 'Signing In...' : 'Sign In' }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import { mapMutations } from "vuex";
import axios from "axios";

export default {
    name: 'Admin',

    data() {
        return {
            adminObj: { 
                username: "", 
                password: "" 
            },
            isLoading: false,
            errors: [],
        }
    },

    created() {
        // If admin is already logged in, redirect to dashboard
        if (this.$store.state.admin) {
            this.$router.push("/admin/dashboard");
        }
    },

    methods: {
        ...mapMutations(["setAdmin"]),

        async handleSubmit(e) {
            e.preventDefault();
            this.errors = [];
            
            // Validate inputs
            if (!this.adminObj.username) {
                this.errors.push('Username is required');
            }
            if (!this.adminObj.password) {
                this.errors.push('Password is required');
            }

            if (this.errors.length > 0) {
                return;
            }
            
            try {
                this.isLoading = true;
                
                // Try to authenticate using the admin credentials
                try {
                    // First try the hardcoded admin credentials
                    const response = await axios.post('/api/admin/login', {
                        username: this.adminObj.username,
                        password: this.adminObj.password
                    });
                    
                    if (response.data && response.data.success) {
                        // Store admin info in Vuex store
                        this.setAdmin(response.data.admin);
                        
                        // Redirect to admin dashboard
                        this.$router.push("/admin/dashboard");
                        return;
                    }
                } catch (adminError) {
                    console.log('Admin login attempt failed, trying user login');
                }
                
                // If admin login fails, try user login with admin role
                try {
                    // Try to login as a user with admin role
                    const userResponse = await axios.get(`/api/users/${this.adminObj.username}`);
                    
                    if (userResponse.data && 
                        userResponse.data.user_password === this.adminObj.password && 
                        userResponse.data.role === 'admin') {
                        
                        // Store admin info in Vuex store
                        const adminInfo = {
                            username: this.adminObj.username,
                            type: "admin",
                            userId: userResponse.data.user_id
                        };
                        
                        this.setAdmin(adminInfo);
                        this.$router.push("/admin/dashboard");
                    } else {
                        this.errors.push('Invalid username or password');
                    }
                } catch (userError) {
                    this.errors.push('Authentication failed');
                    console.error('User login error:', userError);
                }
            } catch (error) {
                console.error('Admin login error:', error);
                if (error.response && error.response.status === 401) {
                    this.errors.push("Invalid username or password");
                } else {
                    this.errors.push("An error occurred during login. Please try again.");
                }
            } finally {
                this.isLoading = false;
            }
        }
    }
}
</script>

<style scoped>
/* Base styles for admin page */
.md-admin-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--md-sys-color-surface-variant);
  padding: var(--md-sys-spacing-medium);
}

.md-admin-container {
  width: 100%;
  max-width: 450px;
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.md-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-medium);
  box-shadow: var(--md-sys-elevation-2);
  overflow: hidden;
}

.md-admin-card {
  padding: var(--md-sys-spacing-large);
}

.md-admin-card__header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-large);
  text-align: center;
}

.md-admin-card__icon {
  font-size: 3rem;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-small);
}

.md-admin-card__title {
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-admin-card__error-box {
  display: flex;
  background-color: var(--md-sys-color-error-container);
  border-radius: var(--md-sys-shape-small);
  padding: var(--md-sys-spacing-small);
  margin-bottom: var(--md-sys-spacing-medium);
  align-items: flex-start;
}

.md-admin-card__error-box-icon {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-error);
}

.md-admin-card__error-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  flex: 1;
}

.md-admin-card__error-item {
  color: var(--md-sys-color-on-error-container);
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.md-admin-card__error-item:last-child {
  margin-bottom: 0;
}

.md-admin-form {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-large);
}

.spin {
  animation: spinner 1s linear infinite;
}

@keyframes spinner {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.md-form-field {
  position: relative;
  width: 100%;
}

.md-form-field__input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-small);
  transition: all 0.3s ease;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.md-form-field__input-container:focus-within {
  border-color: var(--md-sys-color-primary);
  box-shadow: 0 0 0 1px var(--md-sys-color-primary-container);
}

.md-form-field__icon {
  padding: 0 12px;
  color: var(--md-sys-color-on-surface-variant);
}

.md-form-field__input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 16px 12px 16px 0;
  font-size: 1rem;
  color: var(--md-sys-color-on-surface);
  outline: none;
  width: 100%;
}

.md-form-field__input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
  opacity: 0.7;
}

/* Material Design button */
.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: var(--md-sys-shape-small);
  font-weight: 500;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.1px;
  cursor: pointer;
  transition: background-color 0.3s, box-shadow 0.3s;
  border: none;
  outline: none;
  width: 100%;
  gap: 8px;
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-button--primary:hover {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.md-button i {
  font-size: 1.2rem;
}

/* Responsive styles */
@media (max-width: 480px) {
  .md-admin-card {
    padding: var(--md-sys-spacing-medium);
  }
  
  .md-admin-card__icon {
    font-size: 2.5rem;
  }
  
  .md-admin-card__title {
    font-size: 1.3rem;
  }
  
  .md-button {
    padding: 10px 16px;
  }
}
</style>