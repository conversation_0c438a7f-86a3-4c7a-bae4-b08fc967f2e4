<template>
  <div class="md-reviews">
    <h3 class="md-reviews__title">Reviews & Feedback</h3>

    <div v-if="user && !userHasReviewed" class="md-reviews__form">
      <h4 class="md-reviews__subtitle">Add Your Review</h4>
      <div class="md-reviews__rating">
        <span class="md-reviews__label">Rating:</span>
        <div class="md-rating-selector">
          <span
            v-for="star in 5"
            :key="star"
            class="material-icons md-rating-selector__star"
            :class="{'md-rating-selector__star--selected': star <= newReview.rating}"
            @click="newReview.rating = star"
          >
            {{ star <= newReview.rating ? 'star' : 'star_border' }}
          </span>
        </div>
      </div>

      <div class="md-form-field md-form-field--full">
        <label>
          <span class="md-form-field__label">Your Comment</span>
          <textarea
            v-model="newReview.comment"
            class="md-form-field__input md-form-field__input--textarea"
            placeholder="Share your thoughts about this dish..."
            rows="3"
          ></textarea>
        </label>
      </div>

      <div class="md-actions">
        <button
          class="md-button md-button--primary"
          @click="submitReview"
          :disabled="submitting || !newReview.rating"
        >
          <span v-if="submitting">
            <span class="material-icons md-button__icon md-button__icon--spin">refresh</span>
            Submitting...
          </span>
          <span v-else>
            <span class="material-icons md-button__icon">rate_review</span>
            Submit Review
          </span>
        </button>
      </div>
    </div>

    <div v-if="user && userHasReviewed" class="md-reviews__user-review">
      <h4 class="md-reviews__subtitle">Your Review</h4>
      <div class="md-review md-review--user">
        <div class="md-review__header">
          <div class="md-review__info">
            <div class="md-avatar md-avatar--small">
              <template v-if="user.user_image">
                <img :src="user.user_image" alt="Your avatar">
              </template>
              <span v-else class="md-avatar__text">{{ getUserInitials(user.user_name) }}</span>
            </div>
            <div class="md-review__user-info">
              <span class="md-review__name">{{ user.user_name }}</span>
              <div class="md-rating md-rating--small">
                <span
                  v-for="n in 5"
                  :key="n"
                  class="material-icons"
                  :class="{'md-rating__star--filled': n <= userReview.rating}"
                >star</span>
              </div>
            </div>
          </div>
          <div class="md-review__date">
            {{ formatDate(userReview.created_at) }}
          </div>
        </div>

        <p class="md-review__comment">{{ userReview.comment }}</p>

        <div class="md-review__actions">
          <button class="md-button md-button--text" @click="editReview">
            <span class="material-icons">edit</span>
            Edit
          </button>
          <button class="md-button md-button--text md-button--danger" @click="confirmDeleteReview">
            <span class="material-icons">delete</span>
            Delete
          </button>
        </div>
      </div>

      <div v-if="isEditing" class="md-reviews__edit-form">
        <h4 class="md-reviews__subtitle">Edit Your Review</h4>
        <div class="md-reviews__rating">
          <span class="md-reviews__label">Rating:</span>
          <div class="md-rating-selector">
            <span
              v-for="star in 5"
              :key="star"
              class="material-icons md-rating-selector__star"
              :class="{'md-rating-selector__star--selected': star <= editedReview.rating}"
              @click="editedReview.rating = star"
            >
              {{ star <= editedReview.rating ? 'star' : 'star_border' }}
            </span>
          </div>
        </div>

        <div class="md-form-field md-form-field--full">
          <label>
            <span class="md-form-field__label">Your Comment</span>
            <textarea
              v-model="editedReview.comment"
              class="md-form-field__input md-form-field__input--textarea"
              placeholder="Share your thoughts about this dish..."
              rows="3"
            ></textarea>
          </label>
        </div>

        <div class="md-actions">
          <button
            class="md-button md-button--text"
            @click="cancelEdit"
            :disabled="submitting"
          >
            Cancel
          </button>
          <button
            class="md-button md-button--primary"
            @click="updateReview"
            :disabled="submitting || !editedReview.rating"
          >
            <span v-if="submitting">
              <span class="material-icons md-button__icon md-button__icon--spin">refresh</span>
              Updating...
            </span>
            <span v-else>
              <span class="material-icons md-button__icon">save</span>
              Save Changes
            </span>
          </button>
        </div>
      </div>
    </div>

    <div v-if="!user" class="md-reviews__login-notice">
      <span class="material-icons md-reviews__login-icon">account_circle</span>
      <p>Please <router-link to="/login">login</router-link> to add your review</p>
    </div>

    <div class="md-reviews__list">
      <h4 class="md-reviews__subtitle">{{ reviews.length }} {{ reviews.length === 1 ? 'Review' : 'Reviews' }}</h4>

      <div v-if="loading" class="md-reviews__loading">
        <span class="material-icons md-loading-icon">refresh</span>
        <span>Loading reviews...</span>
      </div>

      <div v-else-if="reviews.length === 0" class="md-reviews__empty">
        <span class="material-icons">rate_review</span>
        <p>No reviews yet. Be the first to review this dish!</p>
      </div>

      <template v-else>
        <div
          v-for="review in reviews"
          :key="review.review_id"
          class="md-review"
          v-show="review.user_id !== (user ? user.user_id : null)"
        >
          <div class="md-review__header">
            <div class="md-review__info">
              <div class="md-avatar md-avatar--small">
                <template v-if="review.user_image">
                  <img :src="review.user_image" alt="User avatar">
                </template>
                <span v-else class="md-avatar__text">{{ getUserInitials(review.user_name) }}</span>
              </div>
              <div class="md-review__user-info">
                <span class="md-review__name">{{ review.user_name }}</span>
                <div class="md-rating md-rating--small">
                  <span
                    v-for="n in 5"
                    :key="n"
                    class="material-icons"
                    :class="{'md-rating__star--filled': n <= review.rating}"
                  >star</span>
                </div>
              </div>
            </div>
            <div class="md-review__date">
              {{ formatDate(review.created_at) }}
            </div>
          </div>

          <p class="md-review__comment">{{ review.comment }}</p>

          <!-- Admin Response -->
          <div v-if="review.admin_response" class="md-review__response">
            <div class="md-review__response-header">
              <span class="material-icons">reply</span>
              <span class="md-review__response-label">Restaurant Response</span>
            </div>
            <p class="md-review__response-text">{{ review.admin_response }}</p>
            <small class="md-review__response-date">
              {{ formatDate(review.response_date) }}
            </small>
          </div>

          <div v-if="user" class="md-review__actions">
            <button class="md-button md-button--text md-button--small" @click="reportReview(review)">
              <span class="material-icons">flag</span>
              Report
            </button>
          </div>
        </div>
      </template>
    </div>

    <!-- Report Dialog -->
    <div v-if="showReportDialog" class="md-dialog-overlay" @click.self="showReportDialog = false">
      <div class="md-dialog md-dialog--small">
        <div class="md-dialog__header">
          <h2 class="md-dialog__title">Report Review</h2>
          <button @click="showReportDialog = false" class="md-dialog__close">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="md-dialog__content">
          <p class="md-dialog__text">Please select a reason for reporting this review:</p>

          <div class="md-form-field md-form-field--full">
            <label>
              <span class="md-form-field__label">Reason</span>
              <select v-model="reportReason" class="md-form-field__input">
                <option value="">Select a reason</option>
                <option value="inappropriate">Inappropriate content</option>
                <option value="spam">Spam</option>
                <option value="off-topic">Not related to the food</option>
                <option value="other">Other</option>
              </select>
            </label>
          </div>

          <div v-if="reportReason === 'other'" class="md-form-field md-form-field--full">
            <label>
              <span class="md-form-field__label">Additional Details</span>
              <textarea
                v-model="reportDetails"
                class="md-form-field__input md-form-field__input--textarea"
                rows="3"
              ></textarea>
            </label>
          </div>
        </div>

        <div class="md-dialog__actions">
          <button class="md-button md-button--text" @click="showReportDialog = false">
            Cancel
          </button>
          <button
            class="md-button md-button--primary"
            @click="submitReport"
            :disabled="!reportReason || submittingReport"
          >
            <span v-if="submittingReport">
              <span class="material-icons md-button__icon md-button__icon--spin">refresh</span>
              Submitting...
            </span>
            <span v-else>Submit Report</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <div v-if="showDeleteDialog" class="md-dialog-overlay" @click.self="showDeleteDialog = false">
      <div class="md-dialog md-dialog--small">
        <div class="md-dialog__header">
          <h2 class="md-dialog__title">Delete Review</h2>
          <button @click="showDeleteDialog = false" class="md-dialog__close">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="md-dialog__content">
          <span class="material-icons md-dialog__icon md-dialog__icon--warning">warning</span>
          <p class="md-dialog__text">Are you sure you want to delete your review? This action cannot be undone.</p>
        </div>

        <div class="md-dialog__actions">
          <button class="md-button md-button--text" @click="showDeleteDialog = false">
            Cancel
          </button>
          <button
            class="md-button md-button--danger"
            @click="deleteReview"
            :disabled="deletingReview"
          >
            <span v-if="deletingReview">
              <span class="material-icons md-button__icon md-button__icon--spin">refresh</span>
              Deleting...
            </span>
            <span v-else>Delete</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import apiService from '../services/ApiService';

export default {
  name: 'ReviewsSection',

  props: {
    foodId: {
      type: [Number, String],
      required: true
    }
  },

  data() {
    return {
      reviews: [],
      userReview: null,
      loading: true,
      submitting: false,
      isEditing: false,
      newReview: {
        rating: 0,
        comment: ''
      },
      editedReview: {
        rating: 0,
        comment: ''
      },
      showReportDialog: false,
      reportedReview: null,
      reportReason: '',
      reportDetails: '',
      submittingReport: false,
      showDeleteDialog: false,
      deletingReview: false
    };
  },

  computed: {
    ...mapState(['user']),

    userHasReviewed() {
      return !!this.userReview;
    }
  },

  watch: {
    user() {
      this.checkUserReview();
    },

    foodId() {
      this.loadReviews();
    }
  },

  async created() {
    this.loadReviews();
  },

  methods: {
    async loadReviews() {
      this.loading = true;
      try {
        this.reviews = await apiService.getFoodReviews(this.foodId);
        if (this.user) {
          this.checkUserReview();
        }
      } catch (error) {
        console.error('Error loading reviews:', error);
        this.reviews = [];
      } finally {
        this.loading = false;
      }
    },

    async checkUserReview() {
      if (!this.user) {
        this.userReview = null;
        return;
      }

      try {
        const review = await apiService.getUserReviewForFood(this.user.user_id, this.foodId);
        this.userReview = review;
      } catch (error) {
        console.error('Error checking user review:', error);
        this.userReview = null;
      }
    },

    async submitReview() {
      if (!this.user || this.submitting || !this.newReview.rating) return;

      this.submitting = true;
      try {
        const reviewData = {
          user_id: this.user.user_id,
          food_id: parseInt(this.foodId),
          rating: this.newReview.rating,
          comment: this.newReview.comment
        };

        await apiService.addReview(reviewData);

        // Reset the form
        this.newReview.rating = 0;
        this.newReview.comment = '';

        // Refresh reviews
        await this.loadReviews();

        // Show success message
        this.$emit('notification', {
          type: 'success',
          message: 'Your review has been submitted successfully!'
        });
      } catch (error) {
        console.error('Error submitting review:', error);

        // Show error message
        let errorMsg = 'Failed to submit your review. Please try again.';
        if (error.response && error.response.data && error.response.data.message) {
          errorMsg = error.response.data.message;
        }

        this.$emit('notification', {
          type: 'error',
          message: errorMsg
        });
      } finally {
        this.submitting = false;
      }
    },

    editReview() {
      if (!this.userReview) return;

      this.editedReview = {
        rating: this.userReview.rating,
        comment: this.userReview.comment
      };

      this.isEditing = true;
    },

    cancelEdit() {
      this.isEditing = false;
    },

    async updateReview() {
      if (!this.user || !this.userReview || this.submitting || !this.editedReview.rating) return;

      this.submitting = true;
      try {
        await apiService.updateReview(this.userReview.review_id, this.editedReview);

        // Refresh reviews
        await this.loadReviews();

        // Exit edit mode
        this.isEditing = false;

        // Show success message
        this.$emit('notification', {
          type: 'success',
          message: 'Your review has been updated successfully!'
        });
      } catch (error) {
        console.error('Error updating review:', error);

        // Show error message
        this.$emit('notification', {
          type: 'error',
          message: 'Failed to update your review. Please try again.'
        });
      } finally {
        this.submitting = false;
      }
    },

    confirmDeleteReview() {
      this.showDeleteDialog = true;
    },

    async deleteReview() {
      if (!this.user || !this.userReview || this.deletingReview) return;

      this.deletingReview = true;
      try {
        await apiService.deleteReview(this.userReview.review_id);

        // Refresh reviews
        await this.loadReviews();

        // Close dialog
        this.showDeleteDialog = false;

        // Show success message
        this.$emit('notification', {
          type: 'success',
          message: 'Your review has been deleted.'
        });
      } catch (error) {
        console.error('Error deleting review:', error);

        // Show error message
        this.$emit('notification', {
          type: 'error',
          message: 'Failed to delete your review. Please try again.'
        });
      } finally {
        this.deletingReview = false;
      }
    },

    reportReview(review) {
      this.reportedReview = review;
      this.reportReason = '';
      this.reportDetails = '';
      this.showReportDialog = true;
    },

    async submitReport() {
      if (!this.user || !this.reportedReview || !this.reportReason || this.submittingReport) return;

      this.submittingReport = true;
      try {
        const reason = this.reportReason === 'other'
          ? `${this.reportReason}: ${this.reportDetails}`
          : this.reportReason;

        await apiService.reportReview(this.reportedReview.review_id, reason);

        // Close dialog
        this.showReportDialog = false;
        this.reportedReview = null;

        // Show success message
        this.$emit('notification', {
          type: 'success',
          message: 'Thank you for your report. We will review it shortly.'
        });
      } catch (error) {
        console.error('Error reporting review:', error);

        // Show error message
        this.$emit('notification', {
          type: 'error',
          message: 'Failed to submit your report. Please try again.'
        });
      } finally {
        this.submittingReport = false;
      }
    },

    formatDate(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },

    getUserInitials(name) {
      if (!name) return '?';

      return name.split(' ')
        .map(part => part.charAt(0).toUpperCase())
        .join('')
        .substring(0, 2);
    }
  }
};
</script>

<style scoped>
.md-reviews {
  margin-top: 2rem;
  border-top: 1px solid #e0e0e0;
  padding-top: 1.5rem;
}

.md-reviews__title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.md-reviews__subtitle {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #555;
}

.md-reviews__form,
.md-reviews__user-review,
.md-reviews__edit-form {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.md-reviews__rating {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.md-reviews__label {
  margin-right: 1rem;
  font-weight: 500;
}

.md-rating-selector {
  display: flex;
}

.md-rating-selector__star {
  cursor: pointer;
  color: #ccc;
  font-size: 2rem;
  transition: color 0.2s ease;
}

.md-rating-selector__star:hover,
.md-rating-selector__star--selected {
  color: #f8c01a;
}

.md-form-field {
  margin-bottom: 1.5rem;
}

.md-form-field--full {
  width: 100%;
}

.md-form-field__label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.md-form-field__input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.md-form-field__input:focus {
  border-color: #1976d2;
  outline: none;
}

.md-form-field__input--textarea {
  resize: vertical;
  min-height: 100px;
}

.md-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.md-button--primary {
  background-color: #1976d2;
  color: white;
}

.md-button--primary:hover:not(:disabled) {
  background-color: #1565c0;
}

.md-button--text {
  background-color: transparent;
  color: #1976d2;
}

.md-button--text:hover:not(:disabled) {
  background-color: rgba(25, 118, 210, 0.05);
}

.md-button--danger {
  color: #f44336;
}

.md-button--danger:hover:not(:disabled) {
  background-color: rgba(244, 67, 54, 0.05);
}

.md-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.md-button__icon {
  margin-right: 0.5rem;
}

.md-button__icon--spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.md-button--small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.md-reviews__login-notice {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f5f5f5;
  border-radius: 8px;
  text-align: center;
}

.md-reviews__login-icon {
  font-size: 2.5rem;
  color: #1976d2;
  margin-bottom: 0.5rem;
}

.md-reviews__loading,
.md-reviews__empty {
  text-align: center;
  padding: 2rem;
  color: #757575;
}

.md-loading-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  animation: spin 1s linear infinite;
  display: block;
  margin: 0 auto 1rem;
}

.md-review {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.md-review--user {
  border-left: 4px solid #1976d2;
}

.md-review__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.md-review__info {
  display: flex;
  align-items: center;
}

.md-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-right: 1rem;
}

.md-avatar--small {
  width: 32px;
  height: 32px;
}

.md-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-avatar__text {
  font-weight: 600;
  color: #757575;
  font-size: 0.875rem;
}

.md-review__user-info {
  display: flex;
  flex-direction: column;
}

.md-review__name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.md-rating {
  display: flex;
}

.md-rating--small .material-icons {
  font-size: 1rem;
}

.md-rating__star--filled {
  color: #f8c01a;
}

.md-review__date {
  color: #757575;
  font-size: 0.875rem;
}

.md-review__comment {
  color: #333;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.md-review__response {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-left: 4px solid #ff6b35;
  border-radius: 0 8px 8px 0;
}

.md-review__response-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #ff6b35;
  font-size: 0.875rem;
}

.md-review__response-text {
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
  color: #333;
}

.md-review__response-date {
  color: #666;
  font-size: 0.75rem;
}

.md-review__actions {
  display: flex;
  justify-content: flex-end;
}

.md-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.md-dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.md-dialog--small {
  max-width: 400px;
}

.md-dialog__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.md-dialog__title {
  font-size: 1.25rem;
  margin: 0;
}

.md-dialog__close {
  background: none;
  border: none;
  cursor: pointer;
  color: #757575;
}

.md-dialog__content {
  padding: 1.5rem;
}

.md-dialog__icon {
  font-size: 3rem;
  display: block;
  margin: 0 auto 1rem;
  text-align: center;
}

.md-dialog__icon--warning {
  color: #ff9800;
}

.md-dialog__text {
  margin-bottom: 1.5rem;
}

.md-dialog__actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e0e0e0;
}
</style>
