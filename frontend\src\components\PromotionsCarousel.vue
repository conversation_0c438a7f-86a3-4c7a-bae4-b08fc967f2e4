<template>
  <section class="md-carousel">
    <div class="md-carousel__container" ref="carousel">
      <div 
        v-for="promotion in promotions" 
        :key="promotion.id"
        class="md-carousel__slide"
      >
        <div class="md-card md-card--overlay">
          <img :src="promotion.image" :alt="promotion.title" class="md-card__media">
          <div class="md-card__content">
            <h2 class="md-card__title">{{ promotion.title }}</h2>
            <p class="md-card__text">{{ promotion.description }}</p>
            <button class="md-button md-button--primary">
              {{ promotion.buttonText }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Carousel Navigation -->
    <div class="md-carousel__nav">
      <button 
        v-for="(_, index) in promotions" 
        :key="index"
        class="md-carousel__indicator"
        :class="{ 'md-carousel__indicator--active': currentSlide === index }"
        @click="goToSlide(index)"
      ></button>
    </div>
  </section>
</template>

<script>
export default {
  name: 'PromotionsCarousel',
  data() {
    return {
      currentSlide: 0,
      promotions: [
        {
          id: 1,
          title: 'Taco Tuesday Special',
          description: 'Get 3 tacos for the price of 2 every Tuesday!',
          image: require('@/assets/img/taco-promo.jpg'),
          buttonText: 'Order Now'
        },
        {
          id: 2,
          title: 'Family Combo Deal',
          description: 'Perfect for 4: includes tacos, burritos, and drinks',
          image: require('@/assets/img/family-combo.jpg'),
          buttonText: 'View Deal'
        },
        {
          id: 3,
          title: 'Free Delivery',
          description: 'On orders above $25 - Limited time offer',
          image: require('@/assets/img/delivery.jpg'),
          buttonText: 'Learn More'
        }
      ]
    }
  },
  mounted() {
    this.setupCarousel()
  },
  methods: {
    setupCarousel() {
      const carousel = this.$refs.carousel
      let startX = 0
      let currentX = 0
      let isDragging = false

      carousel.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX
        isDragging = true
      })

      carousel.addEventListener('touchmove', (e) => {
        if (!isDragging) return
        currentX = e.touches[0].clientX
        const diff = startX - currentX
        
        if (Math.abs(diff) > 50) {
          if (diff > 0 && this.currentSlide < this.promotions.length - 1) {
            this.goToSlide(this.currentSlide + 1)
          } else if (diff < 0 && this.currentSlide > 0) {
            this.goToSlide(this.currentSlide - 1)
          }
          isDragging = false
        }
      })

      carousel.addEventListener('touchend', () => {
        isDragging = false
      })
    },
    goToSlide(index) {
      this.currentSlide = index
      this.$refs.carousel.style.transform = `translateX(-${index * 100}%)`
    }
  }
}
</script>

<style scoped>
.md-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  margin: var(--md-sys-spacing-large) 0;
}

.md-carousel__container {
  display: flex;
  transition: transform var(--md-sys-motion-duration-long) var(--md-sys-motion-easing-emphasized);
}

.md-carousel__slide {
  flex: 0 0 100%;
  padding: var(--md-sys-spacing-medium);
}

.md-card {
  position: relative;
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  background-color: var(--md-sys-color-surface);
  box-shadow: var(--md-elevation-level2);
}

.md-card--overlay {
  height: 400px;
}

.md-card__media {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-card__content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--md-sys-spacing-large);
  background: linear-gradient(to top, 
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 60%,
    transparent 100%
  );
  color: white;
}

.md-card__title {
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: 500;
  margin: 0 0 var(--md-sys-spacing-small);
}

.md-card__text {
  font-size: var(--md-sys-typescale-body-large-size);
  margin: 0 0 var(--md-sys-spacing-medium);
  opacity: 0.9;
}

.md-carousel__nav {
  display: flex;
  justify-content: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-medium) 0;
}

.md-carousel__indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface-variant);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
}

.md-carousel__indicator--active {
  background-color: var(--md-sys-color-primary);
  width: 24px;
}

/* Desktop styles */
@media (min-width: 1024px) {
  .md-card--overlay {
    height: 500px;
  }

  .md-card__content {
    padding: var(--md-sys-spacing-extra-large);
  }

  .md-card__title {
    font-size: var(--md-sys-typescale-headline-large-size);
  }
}</style>