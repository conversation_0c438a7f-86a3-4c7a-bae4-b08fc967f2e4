<template>
  <div class="md-chip-scroll">
    <h2 class="md-section-header__title">Categories</h2>
    <div class="md-chip-scroll__container">
      <button
        v-for="category in categories"
        :key="category.id"
        class="md-chip"
        :class="{ 'md-chip--selected': selectedCategory === category.id }"
        @click="selectCategory(category.id)"
      >
        <span class="material-icons-round">{{ category.icon }}</span>
        <span>{{ category.name }}</span>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategoryChips',
  data() {
    return {
      selectedCategory: null,
      categories: [
        { id: 'tacos', name: 'Tacos', icon: 'lunch_dining' },
        { id: 'burritos', name: 'Burritos', icon: 'breakfast_dining' },
        { id: 'nachos', name: 'Nachos', icon: 'bakery_dining' },
        { id: 'drinks', name: 'Drinks', icon: 'local_bar' },
        { id: 'desserts', name: 'Desserts', icon: 'icecream' },
        { id: 'combos', name: 'Combo<PERSON>', icon: 'restaurant' },
        { id: 'sides', name: 'Side<PERSON>', icon: 'tapas' }
      ]
    }
  },
  methods: {
    selectCategory(id) {
      this.selectedCategory = id
      this.$emit('category-selected', id)
    }
  }
}
</script>

<style scoped>
.md-chip-scroll {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: var(--md-sys-spacing-small);
}

.md-chip-scroll__container {
  display: flex;
  gap: var(--md-sys-spacing-small);
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  padding-bottom: var(--md-sys-spacing-small); /* Space for scrollbar */
}

/* Hide scrollbar but keep functionality */
.md-chip-scroll__container::-webkit-scrollbar {
  display: none;
}

.md-chip {
  display: inline-flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  white-space: nowrap;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
  scroll-snap-align: start;
}

.md-chip:hover {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.md-chip--selected {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.material-icons-round {
  font-size: 20px;
}

/* Desktop styles */
@media (min-width: 1024px) {
  .md-chip-scroll__container {
    flex-wrap: wrap;
    justify-content: center;
  }
}</style>