// import connection
import db from "../config/database.js";

// get all Foods
export const getFoods = (result) => {
    db.query("SELECT * FROM food", (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};

// get single Foods
export const getFoodById = (id,result) => {
    db.query("SELECT * FROM food WHERE food_id = ?",[id], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results[0]);
        }
    });
};

// insert Food
export const insertFood = (data, result) => {
    // Ensure all required fields are present
    const foodItem = {
        food_name: data.food_name,
        food_star: data.food_star || '0',
        food_vote: data.food_vote || '0',
        food_price: data.food_price,
        food_discount: data.food_discount || '0.00',
        food_desc: data.food_desc || '',
        food_status: data.food_status || 'normal',
        food_type: data.food_type || '',
        food_category: data.food_category || '',
        food_src: data.food_src || '',
        food_available: data.food_available || 1
    };
    
    db.query("INSERT INTO food SET ?", foodItem, (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// update Food
export const updateFoodById = (data, id, result) => {
    db.query(
        "UPDATE food SET food_name = ?, food_star = ?, food_vote = ?, food_price = ?, food_discount = ?, " +
        "food_desc = ?, food_status = ?, food_type = ?, food_category = ?, food_src = ?, food_available = ? " +
        "WHERE food_id = ?",
        [
            data.food_name,
            data.food_star || '0',
            data.food_vote || '0',
            data.food_price,
            data.food_discount || '0.00',
            data.food_desc || '',
            data.food_status || 'normal',
            data.food_type || '',
            data.food_category || '',
            data.food_src || '',
            data.food_available || 1,
            id
        ],
        (err, results) => {
            if (err) {
                console.log(err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};


// delete Food
export const deleteFoodById = (id,result) => {
    db.query("DELETE FROM food WHERE food_id = ?",[id], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};

// get all food categories
export const getFoodCategories = (result) => {
    db.query("SELECT DISTINCT food_category FROM food WHERE food_category != '' ORDER BY food_category", (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            // Extract just the category names
            const categories = results.map(item => item.food_category);
            result(null, categories);
        }
    });
};

// get all food types
export const getFoodTypes = (result) => {
    db.query("SELECT DISTINCT food_type FROM food WHERE food_type != '' ORDER BY food_type", (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            // Extract just the type names
            const types = results.map(item => item.food_type);
            result(null, types);
        }
    });
};

// update food availability
export const updateFoodAvailability = (id, available, result) => {
    db.query("UPDATE food SET food_available = ? WHERE food_id = ?", [available, id], (err, results) => {
        if (err) {
            console.log(err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};