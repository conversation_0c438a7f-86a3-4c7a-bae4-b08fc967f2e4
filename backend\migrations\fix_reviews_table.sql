-- Create reviews table
CREATE TABLE IF NOT EXISTS `reviews` (
  `review_id` INT AUTO_INCREMENT PRIMARY KEY,
  `food_id` INT NOT NULL,
  `user_id` INT NOT NULL,
  `rating` INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
  `comment` TEXT,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `reported` BOOLEAN DEFAULT FALSE,
  `report_reason` TEXT,
  FOREIGN KEY (`food_id`) REFERENCES `food`(`food_id`) ON DELETE CASCADE,
  FOREIG<PERSON> KEY (`user_id`) REFERENCES `user`(`user_id`) ON DELETE CASCADE,
  UNIQUE KEY `unique_user_food_review` (`user_id`, `food_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Trigger to update food table rating when a review is added
DELIMITER //
CREATE TRIGGER update_food_rating_after_insert 
AFTER INSERT ON reviews
FOR EACH ROW
BEGIN
  UPDATE food 
  SET food_star = (SELECT AVG(rating) FROM reviews WHERE food_id = NEW.food_id),
      food_vote = (SELECT COUNT(*) FROM reviews WHERE food_id = NEW.food_id)
  WHERE food_id = NEW.food_id;
END//

-- Trigger to update food table rating when a review is updated
CREATE TRIGGER update_food_rating_after_update
AFTER UPDATE ON reviews
FOR EACH ROW
BEGIN
  UPDATE food 
  SET food_star = (SELECT AVG(rating) FROM reviews WHERE food_id = NEW.food_id),
      food_vote = (SELECT COUNT(*) FROM reviews WHERE food_id = NEW.food_id)
  WHERE food_id = NEW.food_id;
END//

-- Trigger to update food table rating when a review is deleted
CREATE TRIGGER update_food_rating_after_delete
AFTER DELETE ON reviews
FOR EACH ROW
BEGIN
  UPDATE food 
  SET food_star = COALESCE((SELECT AVG(rating) FROM reviews WHERE food_id = OLD.food_id), 0),
      food_vote = (SELECT COUNT(*) FROM reviews WHERE food_id = OLD.food_id)
  WHERE food_id = OLD.food_id;
END//
DELIMITER ;
