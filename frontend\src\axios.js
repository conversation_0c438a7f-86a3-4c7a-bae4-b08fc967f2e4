import axios from "axios";

window.axios = axios;
axios.defaults.withCredentials = false;

// Set the base URL for API requests
let backendUrl = "http://" + window.location.hostname.toString() + ":5000/api";

// If the app is running in development mode with a specific port, use that
if (process.env.NODE_ENV === 'development' && process.env.VUE_APP_API_URL) {
  backendUrl = process.env.VUE_APP_API_URL;
}

axios.defaults.baseURL = backendUrl;
console.log('API requests will be sent to:', backendUrl);
