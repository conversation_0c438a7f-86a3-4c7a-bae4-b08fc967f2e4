// import functions from User model

import {
    getAllUser,
    getUserByEmail,
    insertUser,
    getUserProfile,
    updateUserProfile,
    getUserAddresses,
    addUserAddress,
    updateUserAddress,
    deleteUserAddress,
    getUserOrders,
    getOrderDetails
} from "../models/UserModel.js";

// get all Users
export const allUsers=(req,res)=>{
    getAllUser((err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};


// get single user
export const showAUser = (req,res)=>{
    getUserByEmail(req.params.email,(err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};

// create user
export const createAccount=(req,res)=>{
    const data = req.body;
    insertUser(data,(err,results)=> {
        if (err) {
            res.send(err);
        }else {
            res.json(results);
        }
    });
};

// Get user profile
export const getProfile = (req, res) => {
    getUserProfile(req.params.id, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};

// Update user profile
export const updateProfile = (req, res) => {
    const id = req.params.id;
    const data = req.body;
    updateUserProfile(id, data, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};

// Get user addresses
export const getAddresses = (req, res) => {
    getUserAddresses(req.params.id, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};

// Add new address
export const addAddress = (req, res) => {
    const userId = req.params.id;
    const data = req.body;
    addUserAddress(userId, data, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};

// Update address
export const updateAddress = (req, res) => {
    const userId = req.params.userId;
    const addressId = req.params.addressId;
    const data = req.body;
    updateUserAddress(userId, addressId, data, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};

// Delete address
export const deleteAddress = (req, res) => {
    const userId = req.params.userId;
    const addressId = req.params.addressId;
    deleteUserAddress(userId, addressId, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};

// Get user orders
export const getOrders = (req, res) => {
    getUserOrders(req.params.id, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};

// Get order details
export const getOrder = (req, res) => {
    const userId = req.params.userId;
    const orderId = req.params.orderId;
    getOrderDetails(userId, orderId, (err, results) => {
        if (err) {
            res.send(err);
        } else {
            res.json(results);
        }
    });
};




