// import connection
import db from "../config/database.js";

// get all items by user id with food details
export const getAllItems = (id,result) => {
    const query = `
        SELECT c.*, f.food_name, f.food_desc, f.food_price,
               f.food_discount, f.food_src, f.food_star, f.food_type
        FROM cart c
        JOIN food f ON c.food_id = f.food_id
        WHERE c.user_id = ?
    `;

    db.query(query, [id], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};

// get a items by user id, food id
export const getAItem = (user,food,result) => {
    db.query("SELECT * FROM cart WHERE user_id = ? AND food_id = ?",[user, food], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};

// insert new item to cart
export const insertToCart = (data,result) => {
    // First check if the item already exists
    db.query("SELECT * FROM cart WHERE user_id = ? AND food_id = ?", [data.user_id, data.food_id], (err, rows) => {
        if (err) {
            console.log(err);
            result(err, null);
            return;
        }

        // If item exists, update quantity instead of inserting
        if (rows.length > 0) {
            const newQty = parseInt(rows[0].item_qty) + parseInt(data.item_qty);
            db.query("UPDATE cart SET item_qty = ? WHERE user_id = ? AND food_id = ?",
                [newQty, data.user_id, data.food_id],
                (err, results) => {
                    if (err) {
                        console.log(err);
                        result(err, null);
                    } else {
                        result(null, { message: "Item quantity updated in cart" });
                    }
                }
            );
        } else {
            // Item doesn't exist, insert new
            db.query("INSERT INTO cart SET ?", data, (err, results) => {
                if (err) {
                    console.log(err);
                    result(err, null);
                } else {
                    result(null, { message: "Item added to cart successfully" });
                }
            });
        }
    });
};

// update qty of a cart item
export const updateCartItemQty = (data,result) => {
    db.query("UPDATE cart SET item_qty = ? WHERE user_id = ? AND food_id = ?",[data.item_qty, data.user_id, data.food_id], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};


// delete cart item
export const deleteItemInCart = (user,food,result) => {
    db.query("DELETE FROM cart WHERE user_id = ? AND food_id = ?",[user,food], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};

// delete all Items
export const deleteAllItemsByUser = (id,result) => {
    db.query("DELETE FROM cart WHERE user_id = ?",[id], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};