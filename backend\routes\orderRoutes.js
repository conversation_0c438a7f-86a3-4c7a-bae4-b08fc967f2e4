import express from 'express';

// Import Controllers (to be created)
import {
    createOrder,
    getOrderDetails,
    // Add other handlers as needed, e.g., getUserOrders
} from '../controllers/orderController.js';

// (Optional) Import authentication middleware if needed
// import { protect } from '../middleware/authMiddleware.js';

const router = express.Router();

// Route to create a new order
// POST /api/orders
// Assuming cart details and customer info are in the request body
router.post('/', createOrder); // Add 'protect' middleware if authentication is required

// Route to get order details by ID
// GET /api/orders/:id
router.get('/:id', getOrderDetails); // Add 'protect' middleware if needed

// Add other routes like getting user's orders, etc.
// router.get('/myorders', protect, getUserOrders);


export default router; 