<template>
  <footer class="md-footer">
    <div class="md-footer__container">
      <div class="md-footer__content">
        <!-- Newsletter Section -->
        <div class="md-newsletter">
          <h3 class="md-footer__title">Receive Event Notifications</h3>
          <form @submit.prevent class="md-newsletter__form">
            <div class="md-text-field">
              <span class="material-icons-round">mail</span>
              <input 
                type="email" 
                placeholder="Enter your email"
                class="md-text-field__input"
              >
            </div>
            <button type="submit" class="md-button md-button--primary">
              Subscribe
            </button>
          </form>
        </div>

        <!-- Two Column Layout -->
        <div class="md-footer__columns">
          <!-- Menu Links -->
          <div class="md-footer__column">
            <h3 class="md-footer__title">Our Menu</h3>
            <nav class="md-footer__nav">
              <router-link to="/menu?category=taco" class="md-footer__link">
                <span class="material-icons-round">chevron_right</span>
                Tacos
              </router-link>
              <router-link to="/menu?category=burrito" class="md-footer__link">
                <span class="material-icons-round">chevron_right</span>
                Burritos
              </router-link>
              <router-link to="/menu?category=nachos" class="md-footer__link">
                <span class="material-icons-round">chevron_right</span>
                Nachos
              </router-link>
              <router-link to="/menu?category=side" class="md-footer__link">
                <span class="material-icons-round">chevron_right</span>
                Side Food
              </router-link>
              <router-link to="/menu?category=dessert" class="md-footer__link">
                <span class="material-icons-round">chevron_right</span>
                Desserts
              </router-link>
              <router-link to="/menu?category=drink" class="md-footer__link">
                <span class="material-icons-round">chevron_right</span>
                Drinks
              </router-link>
            </nav>
          </div>

          <!-- Opening Hours & Contact -->
          <div class="md-footer__column">
            <h3 class="md-footer__title">Opening Hours</h3>
            <ul class="md-footer__hours">
              <li>
                <span class="material-icons-round">schedule</span>
                Monday - Friday: 10:00 - 23:00
              </li>
              <li>
                <span class="material-icons-round">schedule</span>
                Saturday - Sunday: 09:00 - 23:00
              </li>
            </ul>

            <h3 class="md-footer__title">Contact Us</h3>
            <address class="md-footer__contact">
              <p>
                <span class="material-icons-round">location_on</span>
                123 Food Street, Cuisine City
              </p>
              <p>
                <span class="material-icons-round">phone</span>
                <a href="tel:+1234567890" class="md-footer__link">+1 (234) 567-890</a>
              </p>
              <p>
                <span class="material-icons-round">mail</span>
                <a href="mailto:<EMAIL>" class="md-footer__link"><EMAIL></a>
              </p>
            </address>
          </div>
        </div>
      </div>

      <!-- Copyright & Secondary Links -->
      <div class="md-footer__bottom">
        <p class="md-footer__copyright">
          &copy; {{ new Date().getFullYear() }} FoodDeli. All rights reserved.
        </p>
        <nav class="md-footer__secondary-nav">
          <router-link to="/privacy" class="md-footer__secondary-link">Privacy Policy</router-link>
          <router-link to="/terms" class="md-footer__secondary-link">Terms of Service</router-link>
          <router-link to="/contact" class="md-footer__secondary-link">Contact</router-link>
        </nav>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'FooterComponent'
}
</script>

<style scoped>
.md-footer {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  padding: var(--md-sys-spacing-large) var(--md-sys-spacing-medium);
  margin-top: auto;
}

.md-footer__container {
  max-width: 1200px;
  margin: 0 auto;
}

.md-footer__content {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-extra-large);
  margin-bottom: var(--md-sys-spacing-extra-large);
}

.md-footer__columns {
  display: grid;
  gap: var(--md-sys-spacing-extra-large);
}

.md-footer__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-medium);
}

.md-newsletter {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

.md-newsletter__form {
  display: flex;
  gap: var(--md-sys-spacing-small);
  flex-wrap: wrap;
}

.md-text-field {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  background-color: var(--md-sys-color-surface-variant);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border-radius: var(--md-sys-shape-corner-full);
  color: var(--md-sys-color-on-surface-variant);
}

.md-text-field__input {
  flex: 1;
  border: none;
  background: none;
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
  outline: none;
}

.md-footer__nav,
.md-footer__hours,
.md-footer__contact {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-small);
}

.md-footer__link {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  transition: color 0.2s ease;
}

.md-footer__link:hover {
  color: var(--md-sys-color-primary);
}

.md-footer__hours,
.md-footer__contact {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--md-sys-spacing-large);
}

.md-footer__hours li,
.md-footer__contact p {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface-variant);
}

.md-footer__bottom {
  padding-top: var(--md-sys-spacing-large);
  border-top: 1px solid var(--md-sys-color-outline);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--md-sys-spacing-medium);
  text-align: center;
}

.md-footer__copyright {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.md-footer__secondary-nav {
  display: flex;
  gap: var(--md-sys-spacing-medium);
  flex-wrap: wrap;
  justify-content: center;
}

.md-footer__secondary-link {
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  font-size: var(--md-sys-typescale-body-small-size);
}

.md-footer__secondary-link:hover {
  color: var(--md-sys-color-primary);
}

/* Desktop styles */
@media (min-width: 769px) {
  .md-footer {
    padding: var(--md-sys-spacing-extra-large) var(--md-sys-spacing-large);
  }

  .md-footer__columns {
    grid-template-columns: repeat(2, 1fr);
  }

  .md-footer__bottom {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .md-footer__secondary-nav {
    justify-content: flex-end;
  }
}</style>