<template>
  <nav class="md-bottom-nav">
    <router-link to="/" class="md-bottom-nav__item" active-class="md-bottom-nav__item--active">
      <span class="material-icons-round">home</span>
      <span class="md-bottom-nav__label">Home</span>
    </router-link>
    <router-link to="/menu" class="md-bottom-nav__item" active-class="md-bottom-nav__item--active">
      <span class="material-icons-round">restaurant_menu</span>
      <span class="md-bottom-nav__label">Menu</span>
    </router-link>
    <router-link to="/offers" class="md-bottom-nav__item" active-class="md-bottom-nav__item--active">
      <span class="material-icons-round">local_offer</span>
      <span class="md-bottom-nav__label">Offers</span>
    </router-link>
    <router-link to="/cart" class="md-bottom-nav__item" active-class="md-bottom-nav__item--active">
      <span class="material-icons-round">shopping_cart</span>
      <span class="md-bottom-nav__label">Cart</span>
    </router-link>
    <router-link to="/account" class="md-bottom-nav__item" active-class="md-bottom-nav__item--active">
      <span class="material-icons-round">person</span>
      <span class="md-bottom-nav__label">Account</span>
    </router-link>
  </nav>
</template>

<script>
export default {
  name: 'BottomNavigation'
}
</script>

<style scoped>
.md-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background-color: var(--md-sys-color-surface);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: var(--md-elevation-level2);
  z-index: 1000;
}

.md-bottom-nav__item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-extra-small);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  padding: var(--md-sys-spacing-small);
  transition: color var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
}

.md-bottom-nav__item--active {
  color: var(--md-sys-color-primary);
}

.md-bottom-nav__label {
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: 500;
}

/* Hide bottom navigation on desktop */
@media (min-width: 1024px) {
  .md-bottom-nav {
    display: none;
  }
}</style>