{"name": "restaurant-ordering-system", "version": "0.1.0", "homepage": "../", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fontsource/roboto": "^5.2.5", "@material/material-color-utilities": "^0.2.7", "@material/web": "^1.5.1", "axios": "^1.8.4", "core-js": "^3.8.3", "material-icons": "^1.13.14", "material-icons-font": "^2.1.0", "vue": "^3.2.13", "vue-basic-alert": "^1.1.0", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/multi-word-component-names": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}