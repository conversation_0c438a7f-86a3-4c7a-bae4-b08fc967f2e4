<template>
    <div class="md-register-container">
        <div class="md-card">
            <div class="md-card__content">
                <h2 class="md-card__title">Create your account</h2>
                <form id="registerForm" @submit="handleSubmit" novalidate autocomplete="off" class="md-form">
                    <!-- Name field -->
                    <div class="md-form-field">
                        <label for="uName" class="md-form-field__label">Full name</label>
                        <div class="md-text-field">
                            <span class="material-icons-round md-text-field__icon">person</span>
                            <input type="text" name="uName" placeholder="Your full name" id="uName" 
                                class="md-text-field__input" v-model="registerObj.name" />
                        </div>
                        <p class="md-form-field__error" v-if="errorObj.nameErr.length > 0">{{ errorObj.nameErr[0] }}</p>
                    </div>

                    <!-- Email field -->
                    <div class="md-form-field">
                        <label for="uEmail" class="md-form-field__label">Email address</label>
                        <div class="md-text-field">
                            <span class="material-icons-round md-text-field__icon">email</span>
                            <input type="email" name="uEmail" placeholder="<EMAIL>" id="uEmail" 
                                class="md-text-field__input" v-model="registerObj.email" />
                        </div>
                        <p class="md-form-field__error" v-if="errorObj.emailErr.length > 0">{{ errorObj.emailErr[0] }}</p>
                    </div>

                    <!-- Password field -->
                    <div class="md-form-field">
                        <label for="uPass" class="md-form-field__label">Password</label>
                        <div class="md-text-field">
                            <span class="material-icons-round md-text-field__icon">lock</span>
                            <input type="password" name="uPass" placeholder="Enter your password" id="uPass"
                                class="md-text-field__input" v-model="registerObj.pass" />
                        </div>
                        <p class="md-form-field__error" v-if="errorObj.passErr.length > 0">{{ errorObj.passErr[0] }}</p>
                    </div>

                    <!-- Confirm Password field -->
                    <div class="md-form-field">
                        <label for="uPassConfirm" class="md-form-field__label">Confirm password</label>
                        <div class="md-text-field">
                            <span class="material-icons-round md-text-field__icon">lock_reset</span>
                            <input type="password" name="uPassConfirm" placeholder="Enter your password again" id="uPassConfirm"
                                class="md-text-field__input" v-model="registerObj.confirm" />
                        </div>
                        <p class="md-form-field__error" v-if="errorObj.confirmErr.length > 0">{{ errorObj.confirmErr[0] }}</p>
                    </div>

                    <!-- Phone field -->
                    <div class="md-form-field">
                        <label for="uPhone" class="md-form-field__label">Phone number</label>
                        <div class="md-text-field">
                            <span class="material-icons-round md-text-field__icon">phone</span>
                            <input type="tel" name="uPhone" placeholder="+84xxxxxxxxx or 0xxxxxxxxx" id="uPhone"
                                class="md-text-field__input" v-model="registerObj.phone" />
                        </div>
                        <p class="md-form-field__error" v-if="errorObj.phoneErr.length > 0">{{ errorObj.phoneErr[0] }}</p>
                    </div>

                    <!-- Birthday field -->
                    <div class="md-form-field">
                        <label for="uBirth" class="md-form-field__label">Birthday</label>
                        <div class="md-text-field">
                            <span class="material-icons-round md-text-field__icon">cake</span>
                            <input type="date" name="uBirth" id="uBirth" class="md-text-field__input" 
                                @click="availableTime()" v-model="registerObj.birth" />
                        </div>
                        <p class="md-form-field__error" v-if="errorObj.birthErr.length > 0">{{ errorObj.birthErr[0] }}</p>
                    </div>

                    <!-- Gender field -->
                    <div class="md-form-field">
                        <label class="md-form-field__label">Gender</label>
                        <div class="md-radio-group">
                            <label class="md-radio">
                                <input type="radio" name="gender" value="male" id="genderMale" v-model="registerObj.gender" />
                                <span class="md-radio__checkmark"></span>
                                <span class="md-radio__label">Male</span>
                            </label>
                            <label class="md-radio">
                                <input type="radio" name="gender" value="female" id="genderFemale" v-model="registerObj.gender" />
                                <span class="md-radio__checkmark"></span>
                                <span class="md-radio__label">Female</span>
                            </label>
                        </div>
                        <p class="md-form-field__error" v-if="errorObj.genderErr.length > 0">{{ errorObj.genderErr[0] }}</p>
                    </div>

                    <!-- Submit button -->
                    <div class="md-form-actions">
                        <button type="submit" class="md-button md-button--primary">Create Account</button>
                        <p class="md-form-field__helper">Already have an account? <router-link to="/login" @click="scrollToTop()" class="md-link">Login</router-link></p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import apiService from '../services/ApiService';
export default {
    name: "Register",

    data() {
        return {
            registerObj: { name: "", email: "", pass: "", confirm: "", phone: "", birth: "", gender: "" },
            errorObj: { nameErr: [], emailErr: [], passErr: [], confirmErr: [], phoneErr: [], birthErr: [], genderErr: [] },
            matchUser: undefined,

        }
    },

    methods: {
        async getMatchUser(email) {
            try {
                this.matchUser = await apiService.getUserByEmail(email);
            } catch (error) {
                console.error('Error checking existing user:', error);
                this.matchUser = null;
            }
        },

        scrollToTop() {
            window.scrollTo(0, 0);
        },

        availableTime: function () {
            var now = new Date();
            var day = ("0" + now.getDate()).slice(-2);
            var currentMonth = ("0" + (now.getMonth() + 1)).slice(-2);
            var minRange = (now.getFullYear() - 150) + "-" + currentMonth + "-" + day;
            var maxRange = now.getFullYear() + "-" + currentMonth + "-" + day;

            document.getElementById("uBirth").setAttribute("min", minRange);
            document.getElementById("uBirth").setAttribute("max", maxRange);
        },

        resetCheckErr: function () {
            this.errorObj.nameErr = [];
            this.errorObj.emailErr = [];
            this.errorObj.passErr = [];
            this.errorObj.confirmErr = [];
            this.errorObj.phoneErr = [];
            this.errorObj.birthErr = [];
            this.errorObj.genderErr = [];
        },

        checkEmptyErr: function () {
            for (var typeErr in this.errorObj) {
                if (this.errorObj[typeErr].length != 0) {
                    return false;
                }
            }
            return true;
        },

        checkForm: function () {
            this.resetCheckErr();

            // Name validate
            if (!this.registerObj.name) {
                this.errorObj.nameErr.push("Entering a name is required");
            }
            else {
                if (!/^[A-Za-z]+$/.test(this.registerObj.name.replace(/\s/g, ""))) {
                    this.errorObj.nameErr.push('A name can only contain letters');
                }
            }

            // Email validate
            if (!this.registerObj.email) {
                this.errorObj.emailErr.push("Entering a email is required");
            }
            else {
                if (!/[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$/.test(this.registerObj.email)) {
                    this.errorObj.emailErr.push('Email must be valid');
                }
            }

            // Pass validate
            if (!this.registerObj.pass) {
                this.errorObj.passErr.push('Password is required');
            }
            else {
                if (!/[!@#$%^&*]/.test(this.registerObj.pass)) {
                    this.errorObj.passErr.push('Password must contain at least 1 special character');
                }

                if (this.registerObj.pass.length < 8) {
                    this.errorObj.passErr.push('Password must be more than or equal 8 characters');
                }
            }

            // Confirm Pass validate
            if (!this.registerObj.confirm) {
                this.errorObj.confirmErr.push('Confirm password is required');
            }
            else {
                if (this.registerObj.pass !== this.registerObj.confirm) {
                    this.errorObj.confirmErr.push('Confirm password must be match with password');
                }
            }


            // Phone validate
            if (!this.registerObj.phone) {
                this.errorObj.phoneErr.push('Entering phone number is required');
            }
            else {
                // Clean the phone number for validation (remove spaces, hyphens, etc.)
                const phoneClean = this.registerObj.phone.replace(/[\s-]/g, '');
                
                // Check for valid formats: +84 followed by 9 digits (11 total) OR 0 followed by 9 digits (10 total)
                const validPlusFormat = /^\+84\d{9}$/.test(phoneClean);
                const validZeroFormat = /^0\d{9}$/.test(phoneClean);
                
                if (!validPlusFormat && !validZeroFormat) {
                    this.errorObj.phoneErr.push('Phone number must start with +84 (11 digits total) or 0 (10 digits total)');
                }
                
                // Check that phone contains only digits, plus sign if present
                if (!/^[+0-9]+$/.test(phoneClean)) {
                    this.errorObj.phoneErr.push('Phone numbers can only contain digits and + sign if international format');
                }
            }

            // Birth validate
            if (!this.registerObj.birth) {
                this.errorObj.birthErr.push("Entering birthday is required");
            }
            else {
                let minRange = document.getElementById("uBirth").getAttribute("min");
                let maxRange = document.getElementById("uBirth").getAttribute("max");
                let dateMin = new Date(minRange);
                let dateMax = new Date(maxRange);
                let dateInput = new Date(this.registerObj.birth);

                if (dateInput === "Invalid Date") {
                    this.errorObj.birthErr.push("Invalid date input");
                }

                if (dateInput.getTime() < dateMin.getTime() || dateInput.getTime() > dateMax.getTime()) {
                    this.errorObj.birthErr.push("Available birthday range is from pass 150 years to now");
                }
            }

            // Gender validate
            if (!this.registerObj.gender) {
                this.errorObj.genderErr.push("Please select a gender");
            }
        },

        async handleSubmit(e) {
            this.checkForm();

            if (!this.checkEmptyErr()) {
                e.preventDefault();
            } else {
                e.preventDefault();
                await this.getMatchUser(this.registerObj.email);
                if (this.matchUser) {
                    this.errorObj.emailErr.push("Account already exist")
                }
                else {
                    let data = {
                        user_name: this.registerObj.name,
                        user_email: this.registerObj.email,
                        user_phone: this.registerObj.phone,
                        user_password: this.registerObj.pass,
                        user_birth: this.registerObj.birth,
                        user_gender: this.registerObj.gender
                    }
                    try {
                        await apiService.registerUser(data);
                        this.$router.push("/login");
                    } catch (error) {
                        console.error('Registration failed:', error);
                        this.errorObj.emailErr.push("Registration failed. Please try again.");
                    }
                }
            }
        }
    },

};
</script>


<style scoped>
.md-register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--md-sys-spacing-large);
  min-height: 70vh;
}

.md-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-sys-elevation-level2);
  width: 100%;
  max-width: 600px;
  overflow: hidden;
  margin: 0 auto;
}

.md-card__content {
  padding: var(--md-sys-spacing-large);
}

.md-card__title {
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  margin-top: 0;
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-form {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-form-field {
  margin-bottom: var(--md-sys-spacing-small);
}

.md-form-field__label {
  display: block;
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: var(--md-sys-typescale-body-medium-weight);
  margin-bottom: var(--md-sys-spacing-small);
}

.md-text-field {
  display: flex;
  align-items: center;
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: 0 var(--md-sys-spacing-medium);
  transition: box-shadow 0.2s, border 0.2s;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.md-text-field:focus-within {
  border-color: var(--md-sys-color-primary);
  box-shadow: 0 0 0 1px var(--md-sys-color-primary);
}

.md-text-field__icon {
  color: var(--md-sys-color-on-surface-variant);
  margin-right: var(--md-sys-spacing-small);
}

.md-text-field__input {
  flex: 1;
  background: none;
  border: none;
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  padding: var(--md-sys-spacing-medium) 0;
  outline: none;
  width: 100%;
}

.md-text-field__input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
  opacity: 0.7;
}

.md-form-field__error {
  color: var(--md-sys-color-error);
  font-size: var(--md-sys-typescale-body-small-size);
  margin-top: var(--md-sys-spacing-small);
}

.md-radio-group {
  display: flex;
  gap: var(--md-sys-spacing-large);
  margin-top: var(--md-sys-spacing-small);
}

.md-radio {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.md-radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.md-radio__checkmark {
  position: relative;
  height: 20px;
  width: 20px;
  background-color: var(--md-sys-color-surface-variant);
  border: 2px solid var(--md-sys-color-outline);
  border-radius: 50%;
  margin-right: var(--md-sys-spacing-small);
}

.md-radio input[type="radio"]:checked ~ .md-radio__checkmark {
  background-color: var(--md-sys-color-primary-container);
  border-color: var(--md-sys-color-primary);
}

.md-radio input[type="radio"]:checked ~ .md-radio__checkmark:after {
  content: "";
  position: absolute;
  display: block;
  top: 5px;
  left: 5px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--md-sys-color-primary);
}

.md-radio__label {
  color: var(--md-sys-color-on-surface);
  font-size: var(--md-sys-typescale-body-medium-size);
}

.md-form-actions {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
  margin-top: var(--md-sys-spacing-medium);
}

.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  cursor: pointer;
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  letter-spacing: var(--md-sys-typescale-label-large-tracking);
  line-height: var(--md-sys-typescale-label-large-line-height);
  padding: var(--md-sys-spacing-medium) var(--md-sys-spacing-large);
  text-align: center;
  text-transform: uppercase;
  transition: background-color 0.2s;
  user-select: none;
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  width: 100%;
  padding: 14px;
}

.md-button--primary:hover {
  background-color: var(--md-sys-color-primary-dark);
}

.md-form-field__helper {
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-body-medium-size);
  text-align: center;
  margin-top: var(--md-sys-spacing-small);
}

.md-link {
  color: var(--md-sys-color-primary);
  text-decoration: none;
  transition: color 0.2s;
}

.md-link:hover {
  color: var(--md-sys-color-primary-dark);
  text-decoration: underline;
}

/* Add responsive styling */
@media (max-width: 768px) {
  .md-register-container {
    padding: var(--md-sys-spacing-medium);
  }
  
  .md-card__content {
    padding: var(--md-sys-spacing-medium);
  }
  
  .md-radio-group {
    flex-direction: column;
    gap: var(--md-sys-spacing-small);
  }
}
</style>
