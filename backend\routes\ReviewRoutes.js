// Import express
import express from "express";

// Import controllers
import {
    getReviewsByFood,
    getUserReviewForFood,
    createReview,
    updateReview,
    deleteReview,
    reportReview,
    getUserReviews,
    getReportedReviews
} from "../controllers/ReviewController.js";

// Initialize express router
const router = express.Router();

// Routes
// Get all reviews for a food item
router.get('/foods/:foodId/reviews', getReviewsByFood);

// Get user's review for a specific food item
router.get('/users/:userId/foods/:foodId/review', getUserReviewForFood);

// Add a new review
router.post('/reviews', createReview);

// Update a review
router.put('/reviews/:reviewId', updateReview);

// Delete a review
router.delete('/reviews/:reviewId', deleteReview);

// Report a review
router.post('/reviews/:reviewId/report', reportReview);

// Get all reviews by a user
router.get('/users/:userId/reviews', getUserReviews);

// Get all reported reviews (admin only)
router.get('/admin/reviews/reported', getReportedReviews);

// Export router
export default router;
