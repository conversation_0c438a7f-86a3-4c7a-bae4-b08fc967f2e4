<template>
  <div class="md-admin-menu-management">
    <div class="md-admin-header">
      <div class="md-admin-header__title-group">
        <h1 class="md-admin-header__title">
          <i class="material-icons md-admin-header__icon">restaurant_menu</i>
          Menu Management
        </h1>
        <p class="md-admin-header__subtitle">Manage food items in your restaurant</p>
      </div>

      <div class="md-admin-header__actions">
        <button class="md-button md-button--text" @click="navigateToDashboard">
          <i class="material-icons">dashboard</i>
          <span>Dashboard</span>
        </button>

        <button class="md-button md-button--text" @click="refreshData">
          <i class="material-icons">refresh</i>
          <span>Refresh</span>
        </button>

        <button class="md-button md-button--outlined md-button--warn" @click="handleLogout()">
          <i class="material-icons">logout</i>
          <span>Logout</span>
        </button>
      </div>
    </div>

    <!-- Food Items List -->
    <div class="md-card md-admin-menu">
      <div class="md-admin-menu__header">
        <h2 class="md-admin-menu__title">
          <i class="material-icons">fastfood</i>
          Food Items
        </h2>
        <button class="md-button md-button--primary" @click="openAddFoodDialog">
          <i class="material-icons">add</i>
          <span>Add New Item</span>
        </button>
      </div>

      <div class="md-admin-menu__content">
        <div v-if="isLoading" class="md-loading-container">
          <div class="md-loading-spinner"></div>
          <p>Loading menu items...</p>
        </div>

        <div v-else-if="foods.length === 0" class="md-admin-empty-state">
          <i class="material-icons">restaurant_menu</i>
          <p>No menu items found</p>
          <button class="md-button md-button--primary" @click="openAddFoodDialog">
            <i class="material-icons">add</i>
            <span>Add First Item</span>
          </button>
        </div>

        <div v-else class="md-admin-table-container">
          <table class="md-admin-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Image</th>
                <th>Name</th>
                <th>Description</th>
                <th>Price</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="food in foods" :key="food.food_id">
                <td><span class="md-food-id">#{{ food.food_id }}</span></td>
                <td>
                  <div class="md-food-image">
                    <img
                      :src="getImagePath(food)"
                      :alt="food.food_name"
                      class="md-food-image__img"
                      @error="(e) => handleListImageError(e, food)"
                    />
                  </div>
                </td>
                <td>{{ food.food_name }}</td>
                <td class="md-food-description">
                  {{ food.food_desc || food.food_description || 'No description available' }}
                </td>
                <td><span class="md-food-price">${{ parseFloat(food.food_price).toFixed(2) }}</span></td>
                <td class="md-admin-table__actions">
                  <button class="md-button md-button--icon" @click="openEditFoodDialog(food)" title="Edit Item">
                    <i class="material-icons">edit</i>
                  </button>
                  <button class="md-button md-button--icon md-button--danger" @click="confirmDeleteFood(food)" title="Delete Item">
                    <i class="material-icons">delete</i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Add/Edit Food Dialog -->
    <div v-if="showFoodDialog" class="md-dialog-overlay" @click.self="closeFoodDialog">
      <div class="md-dialog">
        <div class="md-dialog__header">
          <h2 class="md-dialog__title">
            <i class="material-icons">{{ isEditing ? 'edit' : 'add' }}</i>
            {{ isEditing ? 'Edit Menu Item' : 'Add New Menu Item' }}
          </h2>
          <button class="md-button md-button--icon" @click="closeFoodDialog">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div v-if="errors.length" class="md-dialog__error-box">
          <div class="md-dialog__error-box-icon">
            <i class="material-icons">error</i>
          </div>
          <ul class="md-dialog__error-list">
            <li v-for="error in errors" :key="error" class="md-dialog__error-item">
              {{ error }}
            </li>
          </ul>
        </div>

        <div class="md-dialog__content">
          <form id="foodForm" @submit.prevent="submitFoodForm" novalidate autocomplete="off" class="md-form">
            <!-- Food Name -->
            <div class="md-form-field">
              <label class="md-form-field__label">Food Name</label>
              <div class="md-form-field__input-container">
                <i class="material-icons md-form-field__icon">fastfood</i>
                <input
                  type="text"
                  class="md-form-field__input"
                  placeholder="Enter food name"
                  v-model="foodItem.food_name"
                />
              </div>
            </div>

            <!-- Food Price -->
            <div class="md-form-field">
              <label class="md-form-field__label">Price</label>
              <div class="md-form-field__input-container">
                <i class="material-icons md-form-field__icon">attach_money</i>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  class="md-form-field__input"
                  placeholder="Enter price"
                  v-model="foodItem.food_price"
                />
              </div>
            </div>

            <!-- Food Description -->
            <div class="md-form-field">
              <label class="md-form-field__label">Description</label>
              <div class="md-form-field__input-container">
                <i class="material-icons md-form-field__icon">description</i>
                <textarea
                  class="md-form-field__input md-form-field__textarea"
                  placeholder="Enter food description"
                  v-model="foodItem.food_description"
                ></textarea>
              </div>
            </div>

            <!-- Food Image -->
            <div class="md-form-field">
              <label class="md-form-field__label">Food Image</label>
              <div class="md-image-upload">
                <div v-if="foodItem.food_image" class="md-image-upload__preview">
                  <img
                    :src="getPreviewImagePath()"
                    class="md-image-upload__img"
                    @error="handleImageError"
                    ref="imagePreview"
                  />
                  <button
                    type="button"
                    class="md-image-upload__remove"
                    @click="removeImage"
                  >
                    <i class="material-icons">close</i>
                  </button>
                </div>
                <div v-else class="md-image-upload__placeholder">
                  <i class="material-icons">image</i>
                  <p>No image uploaded</p>
                </div>

                <label class="md-image-upload__button">
                  <i class="material-icons">cloud_upload</i>
                  <span>{{ foodItem.food_image ? 'Change Image' : 'Upload Image' }}</span>
                  <input
                    type="file"
                    ref="imageInput"
                    accept="image/*"
                    class="md-image-upload__input"
                    @change="handleImageUpload"
                  />
                </label>

                <div v-if="uploadProgress > 0 && uploadProgress < 100" class="md-image-upload__progress">
                  <div class="md-image-upload__progress-bar" :style="{width: uploadProgress + '%'}"></div>
                </div>
              </div>
            </div>

            <!-- Or use external URL -->
            <div class="md-form-field">
              <label class="md-form-field__label">Or use external image URL</label>
              <div class="md-form-field__input-container">
                <i class="material-icons md-form-field__icon">link</i>
                <input
                  type="text"
                  class="md-form-field__input"
                  placeholder="Enter image URL"
                  v-model="foodItem.food_image"
                />
              </div>
            </div>

            <div class="md-dialog__actions">
              <button type="button" class="md-button md-button--text" @click="closeFoodDialog">
                Cancel
              </button>
              <button type="submit" class="md-button md-button--primary" :disabled="isSaving">
                <i class="material-icons" v-if="!isSaving">{{ isEditing ? 'save' : 'add' }}</i>
                <i class="material-icons spin" v-else>sync</i>
                <span>{{ isSaving ? 'Saving...' : (isEditing ? 'Save Changes' : 'Add Item') }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <div v-if="showDeleteDialog" class="md-dialog-overlay" @click.self="closeDeleteDialog">
      <div class="md-dialog md-dialog--confirm">
        <div class="md-dialog__header">
          <h2 class="md-dialog__title">
            <i class="material-icons">delete</i>
            Confirm Deletion
          </h2>
        </div>

        <div class="md-dialog__content">
          <p class="md-dialog__message">
            Are you sure you want to delete <strong>"{{ selectedFood.food_name }}"</strong>?
            This action cannot be undone.
          </p>
        </div>

        <div class="md-dialog__actions">
          <button class="md-button md-button--text" @click="closeDeleteDialog">
            Cancel
          </button>
          <button class="md-button md-button--danger" @click="deleteFood" :disabled="isDeleting">
            <i class="material-icons" v-if="!isDeleting">delete</i>
            <i class="material-icons spin" v-else>sync</i>
            <span>{{ isDeleting ? 'Deleting...' : 'Delete' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { mapState, mapMutations } from "vuex";

export default {
  name: 'MenuManagement',

  data() {
    return {
      foods: [],
      isLoading: true,
      isSaving: false,
      isDeleting: false,
      isUploading: false,
      showFoodDialog: false,
      showDeleteDialog: false,
      isEditing: false,
      errors: [],
      imageError: false,
      selectedFood: null,
      foodItem: {
        food_name: "",
        food_price: "",
        food_description: "",
        food_image: ""
      },
      uploadProgress: 0,
      selectedFile: null
    }
  },

  created() {
    // Check if admin is logged in
    if (!this.admin || this.admin.type !== 'admin') {
      console.log('Not authenticated as admin, redirecting to admin login');
      this.$router.push("/admin");
      return;
    }

    this.loadFoods();
  },

  computed: {
    ...mapState(["admin"]),
  },

  methods: {
    ...mapMutations(["setAdmin"]),

    async loadFoods() {
      try {
        this.isLoading = true;
        const response = await axios.get('/api/foods');
        this.foods = response.data;

        // Debug logging to check data structure
        console.log('Fetched foods for admin:', this.foods);
        if (this.foods.length > 0) {
          console.log('Sample food data:', this.foods[0]);
          console.log('Image field (food_src):', this.foods[0].food_src);
          console.log('Description field (food_desc):', this.foods[0].food_desc);
        }
      } catch (error) {
        console.error('Error loading foods:', error);
      } finally {
        this.isLoading = false;
      }
    },

    refreshData() {
      this.loadFoods();
    },

    navigateToDashboard() {
      this.$router.push('/admin/dashboard');
    },

    handleLogout() {
      this.setAdmin(null);
      this.$router.push("/admin");
    },

    openAddFoodDialog() {
      this.isEditing = false;
      this.foodItem = {
        food_name: "",
        food_price: "",
        food_description: "",
        food_image: ""
      };
      this.errors = [];
      this.showFoodDialog = true;
    },

    openEditFoodDialog(food) {
      this.isEditing = true;
      this.foodItem = {
        food_id: food.food_id,
        food_name: food.food_name,
        food_price: food.food_price,
        food_description: food.food_desc || food.food_description || "",
        food_image: food.food_src || food.food_image || ""
      };
      this.errors = [];
      this.showFoodDialog = true;
    },

    closeFoodDialog() {
      this.showFoodDialog = false;
      this.errors = [];
      this.selectedFile = null;
      this.uploadProgress = 0;
      this.imageError = false;
    },

    confirmDeleteFood(food) {
      this.selectedFood = food;
      this.showDeleteDialog = true;
    },

    closeDeleteDialog() {
      this.showDeleteDialog = false;
      this.selectedFood = null;
    },

    handleImageError() {
      this.imageError = true;
    },

    // Get image path for food items (similar to Menu.vue)
    getImagePath(food) {
      // First, handle null/undefined data gracefully
      if (!food) {
        return require('../assets/images/nachos-img.png');
      }

      // Handle image path formats from both API and database
      const imageName = food.food_src || food.food_image || food.image;

      // If it's a full URL, use it directly
      if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
        return imageName;
      }

      // Handle cases where database returns 'food-img.png' which doesn't exist
      if (imageName === 'food-img.png') {
        // Get category-based default instead
        const category = (food.food_category || food.category || 'other').toLowerCase();
        return this.getCategoryDefaultImage(category);
      }

      // Use a safer approach with a default fallback image
      if (imageName) {
        try {
          return require(`../assets/images/${imageName}`);
        } catch (error) {
          // Image not found, will select category-based default
          console.log('Image not found:', imageName);
        }
      }

      // Select a default image based on category
      const category = (food.food_category || food.category || 'other').toLowerCase();
      return this.getCategoryDefaultImage(category);
    },

    // Helper method to get default images by category
    getCategoryDefaultImage(category) {
      // These are guaranteed to exist in our assets
      if (category.includes('taco')) {
        return require('../assets/images/taco-img.png');
      } else if (category.includes('burrito')) {
        return require('../assets/images/burrito-img.png');
      } else if (category.includes('nacho')) {
        return require('../assets/images/nachos-img.png');
      } else if (category.includes('side') || category.includes('salad')) {
        return require('../assets/images/salad-img.png');
      } else if (category.includes('dessert')) {
        return require('../assets/images/dessert-img.png');
      } else if (category.includes('coca') || category.includes('drink')) {
        return require('../assets/images/coca-img.png');
      }

      // Default fallback image for any other category
      return require('../assets/images/nachos-img.png');
    },

    // Handle image error in list view
    handleListImageError(event, food) {
      console.log('Image load error for food:', food.food_name, 'trying fallback');
      const category = (food.food_category || food.category || 'other').toLowerCase();
      event.target.src = this.getCategoryDefaultImage(category);
    },

    // Get preview image path for dialog
    getPreviewImagePath() {
      const imageName = this.foodItem.food_image;

      // If it's a full URL, use it directly
      if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
        return imageName;
      }

      // If it's a data URL (from file upload), use it directly
      if (imageName && imageName.startsWith('data:')) {
        return imageName;
      }

      // Try to require the image from assets
      if (imageName) {
        try {
          return require(`../assets/images/${imageName}`);
        } catch (error) {
          console.log('Preview image not found:', imageName);
          return require('../assets/images/nachos-img.png');
        }
      }

      return require('../assets/images/nachos-img.png');
    },

    validateFoodForm() {
      this.errors = [];

      if (!this.foodItem.food_name) {
        this.errors.push('Food name is required');
      }

      if (!this.foodItem.food_price) {
        this.errors.push('Price is required');
      } else if (isNaN(this.foodItem.food_price) || parseFloat(this.foodItem.food_price) < 0) {
        this.errors.push('Price must be a valid positive number');
      }

      return this.errors.length === 0;
    },

    async submitFoodForm() {
      if (!this.validateFoodForm()) {
        return;
      }

      try {
        this.isSaving = true;

        // Upload any pending image file first
        if (this.selectedFile && this.uploadProgress === 0) {
          await this.uploadImage();
        }

        // Prepare food item data with proper types and correct field names
        const foodData = {
          food_name: this.foodItem.food_name,
          food_price: parseFloat(this.foodItem.food_price),
          food_desc: this.foodItem.food_description || null,  // Map to database field
          food_src: this.foodItem.food_image || null          // Map to database field
        };

        if (this.isEditing) {
          await axios.put(`/api/foods/${this.foodItem.food_id}`, foodData);
        } else {
          await axios.post('/api/foods', foodData);
        }

        await this.loadFoods();
        this.closeFoodDialog();
      } catch (error) {
        console.error('Error saving food item:', error);
        this.errors.push('An error occurred while saving the food item. Please try again.');
      } finally {
        this.isSaving = false;
      }
    },

    async deleteFood() {
      if (!this.selectedFood) return;

      try {
        this.isDeleting = true;
        await axios.delete(`/api/foods/${this.selectedFood.food_id}`);
        await this.loadFoods();
        this.closeDeleteDialog();
      } catch (error) {
        console.error('Error deleting food item:', error);
        this.errors.push('An error occurred while deleting the food item. Please try again.');
      } finally {
        this.isDeleting = false;
      }
    },

    removeImage() {
      this.foodItem.food_image = "";
      this.selectedFile = null;
      this.uploadProgress = 0;
    },

    async handleImageUpload(event) {
      this.selectedFile = event.target.files[0];
      if (!this.selectedFile) return;

      // Show preview immediately using FileReader
      const reader = new FileReader();
      reader.onload = (e) => {
        // This is just a preview, not the permanent URL
        const previewUrl = e.target.result;

        // Display preview but don't save to foodItem.food_image yet
        this.$refs.imagePreview?.setAttribute('src', previewUrl);
      };
      reader.readAsDataURL(this.selectedFile);

      // Upload the file to the server
      await this.uploadImage();
    },

    async uploadImage() {
      if (!this.selectedFile) return;

      try {
        this.isUploading = true;
        this.uploadProgress = 0;

        // Create form data
        const formData = new FormData();
        formData.append('image', this.selectedFile);

        // Upload image with progress tracking
        const response = await axios.post('/api/upload/food-image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          }
        });

        if (response.data.success) {
          // Set the actual image URL returned from the server
          this.foodItem.food_image = response.data.file.fullUrl;
          this.imageError = false;
        } else {
          throw new Error('Upload failed');
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        this.errors.push('Error uploading image. Please try again.');
        this.imageError = true;
      } finally {
        this.isUploading = false;
      }
    }
  }
}
</script>

<style scoped>
/* Menu Management Styles */
.md-admin-menu-management {
  padding: var(--md-sys-spacing-medium);
}

.md-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-large);
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-medium);
}

.md-admin-header__title-group {
  display: flex;
  flex-direction: column;
}

.md-admin-header__title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.md-admin-header__icon {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-admin-header__subtitle {
  margin: var(--md-sys-spacing-xsmall) 0 0;
  color: var(--md-sys-color-on-surface-variant);
}

.md-admin-header__actions {
  display: flex;
  gap: var(--md-sys-spacing-small);
  flex-wrap: wrap;
}

.md-admin-menu {
  margin-bottom: var(--md-sys-spacing-large);
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-medium);
  box-shadow: var(--md-sys-elevation-2);
  overflow: hidden;
}

.md-admin-menu__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--md-sys-spacing-medium);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-admin-menu__title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
}

.md-admin-menu__title i {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-admin-menu__content {
  padding: var(--md-sys-spacing-medium);
}

.md-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-xlarge);
}

.md-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--md-sys-color-surface-variant);
  border-top: 4px solid var(--md-sys-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--md-sys-spacing-medium);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.md-admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-xlarge);
  color: var(--md-sys-color-on-surface-variant);
}

.md-admin-empty-state i {
  font-size: 3rem;
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-admin-empty-state p {
  margin-bottom: var(--md-sys-spacing-medium);
  font-size: 1.1rem;
}

.md-admin-table-container {
  overflow-x: auto;
}

.md-admin-table {
  width: 100%;
  border-collapse: collapse;
}

.md-admin-table th,
.md-admin-table td {
  padding: var(--md-sys-spacing-medium);
  text-align: left;
  vertical-align: middle;
}

.md-admin-table th {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  font-weight: 500;
}

.md-admin-table tr {
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-admin-table tr:last-child {
  border-bottom: none;
}

.md-admin-table__actions {
  display: flex;
  gap: var(--md-sys-spacing-xsmall);
}

.md-admin-table__empty {
  text-align: center;
  padding: var(--md-sys-spacing-xlarge);
  color: var(--md-sys-color-on-surface-variant);
}

.md-admin-table__empty i {
  font-size: 2rem;
  margin-bottom: var(--md-sys-spacing-small);
}

.md-food-id {
  font-family: monospace;
  font-weight: 500;
}

.md-food-image {
  width: 60px;
  height: 60px;
  border-radius: var(--md-sys-shape-small);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--md-sys-color-surface-variant);
}

.md-food-image__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-food-image__placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--md-sys-color-on-surface-variant);
}

.md-food-description {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.md-food-price {
  font-weight: 500;
  color: var(--md-sys-color-primary);
}

/* Dialog Styles */
.md-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.md-dialog {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-large);
  box-shadow: var(--md-sys-elevation-3);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: dialogFadeIn 0.3s ease-out;
}

.md-dialog--confirm {
  max-width: 400px;
}

@keyframes dialogFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.md-dialog__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--md-sys-spacing-medium);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-dialog__title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
}

.md-dialog__title i {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-dialog__content {
  padding: var(--md-sys-spacing-medium);
}

.md-dialog__actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-medium);
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.md-dialog__error-box {
  display: flex;
  background-color: var(--md-sys-color-error-container);
  border-radius: var(--md-sys-shape-small);
  padding: var(--md-sys-spacing-small);
  margin: 0 var(--md-sys-spacing-medium);
  align-items: flex-start;
}

.md-dialog__error-box-icon {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-error-container);
}

.md-dialog__error-list {
  margin: 0;
  padding-left: var(--md-sys-spacing-medium);
  color: var(--md-sys-color-on-error-container);
}

.md-dialog__error-item {
  margin-bottom: var(--md-sys-spacing-xsmall);
}

.md-dialog__error-item:last-child {
  margin-bottom: 0;
}

.md-dialog__message {
  margin: var(--md-sys-spacing-medium) 0;
  line-height: 1.5;
}

.md-form {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-medium);
}

.md-form-field {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-xsmall);
}

.md-form-field__label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--md-sys-color-on-surface-variant);
}

.md-form-field__input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-small);
  background-color: var(--md-sys-color-surface);
  padding: 0 var(--md-sys-spacing-small);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.md-form-field__input-container:focus-within {
  border-color: var(--md-sys-color-primary);
  box-shadow: 0 0 0 1px var(--md-sys-color-primary);
}

.md-form-field__icon {
  margin-right: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface-variant);
}

.md-form-field__input {
  flex: 1;
  border: none;
  padding: var(--md-sys-spacing-small) 0;
  background: transparent;
  color: var(--md-sys-color-on-surface);
  font-size: 1rem;
  width: 100%;
}

.md-form-field__input:focus {
  outline: none;
}

.md-form-field__textarea {
  min-height: 100px;
  resize: vertical;
  padding: var(--md-sys-spacing-small);
}

.md-image-upload {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-small);
}

.md-image-upload__preview {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: var(--md-sys-shape-medium);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--md-sys-color-surface-variant);
}

.md-image-upload__img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.md-image-upload__remove {
  position: absolute;
  top: var(--md-sys-spacing-small);
  right: var(--md-sys-spacing-small);
  background-color: var(--md-sys-color-surface);
  border: none;
  padding: var(--md-sys-spacing-xsmall);
  border-radius: var(--md-sys-shape-small);
  cursor: pointer;
}

.md-image-upload__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--md-sys-color-on-surface-variant);
  text-align: center;
}

.md-image-upload__placeholder i {
  font-size: 3rem;
  margin-bottom: var(--md-sys-spacing-small);
}

.md-image-upload__button {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-xsmall);
  padding: var(--md-sys-spacing-small);
  border: none;
  border-radius: var(--md-sys-shape-small);
  background-color: var(--md-sys-color-surface);
  cursor: pointer;
}

.md-image-upload__input {
  display: none;
}

.md-image-upload__progress {
  width: 100%;
  height: 4px;
  border-radius: var(--md-sys-shape-small);
  background-color: var(--md-sys-color-surface-variant);
  overflow: hidden;
}

.md-image-upload__progress-bar {
  height: 100%;
  background-color: var(--md-sys-color-primary);
  transition: width 0.2s;
}

.md-image-preview {
  width: 100%;
  height: 200px;
  border-radius: var(--md-sys-shape-medium);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--md-sys-color-surface-variant);
}

.md-image-preview__img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.md-image-preview__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--md-sys-color-on-surface-variant);
  text-align: center;
}

.md-image-preview__placeholder i {
  font-size: 3rem;
  margin-bottom: var(--md-sys-spacing-small);
}

/* Material Design Button */
.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-xsmall);
  border: none;
  border-radius: var(--md-sys-shape-small);
  padding: 0 var(--md-sys-spacing-medium);
  height: 36px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
}

.md-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-button--primary:hover:not(:disabled) {
  background-color: var(--md-sys-color-primary-hover, var(--md-sys-color-primary));
  box-shadow: var(--md-sys-elevation-1);
}

.md-button--outlined {
  background-color: transparent;
  border: 1px solid var(--md-sys-color-outline);
  color: var(--md-sys-color-primary);
}

.md-button--outlined:hover:not(:disabled) {
  background-color: var(--md-sys-color-surface-variant);
}

.md-button--text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.md-button--text:hover:not(:disabled) {
  background-color: var(--md-sys-color-surface-variant);
}

.md-button--danger {
  background-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error);
}

.md-button--warn {
  color: var(--md-sys-color-error);
}

.md-button--icon {
  width: 36px;
  padding: 0;
}

.spin {
  animation: spin 1s linear infinite;
}
</style>
