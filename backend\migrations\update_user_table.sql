-- Add role and is_active columns to the user table
-- Run this file manually to add the required columns

-- Add role column if it doesn't exist
ALTER TABLE user ADD COLUMN role VARCHAR(50) DEFAULT 'user';

-- Add is_active column if it doesn't exist
ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFAULT TRUE;

-- Update admin users to have admin role
UPDATE user SET role = 'admin' WHERE user_id = 1;

-- Make sure all existing users are active (using primary key to comply with safe update mode)
UPDATE user SET is_active = TRUE WHERE user_id > 0 AND is_active IS NULL;
