<template>
    <div class="md-admin-dashboard">
        <div class="md-admin-header">
            <div class="md-admin-header__title-group">
                <h1 class="md-admin-header__title">
                    <i class="material-icons md-admin-header__icon">dashboard</i>
                    Dashboard
                </h1>
                <p class="md-admin-header__subtitle">Manage your restaurant orders</p>
            </div>

            <div class="md-admin-header__actions">
                <button class="md-button md-button--text" @click="navigateToUserManagement">
                    <i class="material-icons">people</i>
                    <span>User Management</span>
                </button>

                <button class="md-button md-button--text" @click="navigateToMenuManagement">
                    <i class="material-icons">restaurant_menu</i>
                    <span>Menu Management</span>
                </button>

                <button class="md-button md-button--text" @click="navigateToOrderManagement">
                    <i class="material-icons">receipt_long</i>
                    <span>Order Management</span>
                </button>

                <button class="md-button md-button--text" @click="navigateToReviewManagement">
                    <i class="material-icons">rate_review</i>
                    <span>Review Management</span>
                </button>

                <button class="md-button md-button--text" @click="refreshData">
                    <i class="material-icons">refresh</i>
                    <span>Refresh</span>
                </button>

                <button class="md-button md-button--outlined md-button--warn" @click="handleLogout()">
                    <i class="material-icons">logout</i>
                    <span>Logout</span>
                </button>
            </div>
        </div>

        <div class="md-admin-stats">
            <div class="md-stat-card">
                <div class="md-stat-card__icon md-stat-card__icon--pending">
                    <i class="material-icons">pending</i>
                </div>
                <div class="md-stat-card__content">
                    <h3 class="md-stat-card__value">{{ getPendingOrders() }}</h3>
                    <p class="md-stat-card__label">Pending Orders</p>
                </div>
            </div>

            <div class="md-stat-card">
                <div class="md-stat-card__icon md-stat-card__icon--completed">
                    <i class="material-icons">task_alt</i>
                </div>
                <div class="md-stat-card__content">
                    <h3 class="md-stat-card__value">{{ getCompletedOrders() }}</h3>
                    <p class="md-stat-card__label">Completed Orders</p>
                </div>
            </div>

            <div class="md-stat-card">
                <div class="md-stat-card__icon md-stat-card__icon--revenue">
                    <i class="material-icons">payments</i>
                </div>
                <div class="md-stat-card__content">
                    <h3 class="md-stat-card__value">${{ getTotalRevenue() }}</h3>
                    <p class="md-stat-card__label">Total Revenue</p>
                </div>
            </div>
        </div>

        <div class="md-card md-admin-orders">
            <div class="md-admin-orders__header">
                <h2 class="md-admin-orders__title">
                    <i class="material-icons">receipt_long</i>
                    Recent Orders
                </h2>
                <div class="md-admin-orders__actions">
                    <button class="md-button md-button--text" @click="navigateToOrderManagement">
                        <i class="material-icons">launch</i>
                        <span>View All Orders</span>
                    </button>
                </div>
            </div>

            <div class="md-admin-orders__content">
                <div class="md-admin-table-container">
                    <table class="md-admin-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Items</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="order in recentOrders" :key="order.bill_id">
                                <td><span class="md-order-id">#{{ order.bill_id }}</span></td>
                                <td>User #{{ order.user_id }}</td>
                                <td>{{ getOrderItemCount() }} items</td>
                                <td><span class="md-order-price">${{ parseFloat(order.bill_total || 0).toFixed(2) }}</span></td>
                                <td>
                                    <span :class="getStatusClassFromBillStatus(order.bill_status)">
                                        {{ getStatusLabelFromBillStatus(order.bill_status) }}
                                    </span>
                                </td>
                                <td class="md-admin-table__actions">
                                    <button class="md-button md-button--icon" @click="viewDetails(order.bill_id)" title="View Details">
                                        <i class="material-icons">visibility</i>
                                    </button>
                                    <button v-if="order.bill_status === 1" class="md-button md-button--icon md-button--success" @click="markAsPaid(order.bill_id)" title="Mark as Paid">
                                        <i class="material-icons">check_circle</i>
                                    </button>
                                    <button v-if="order.bill_status === 1" class="md-button md-button--icon md-button--danger" @click="cancelOrder(order.bill_id)" title="Cancel Order">
                                        <i class="material-icons">cancel</i>
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="recentOrders.length === 0">
                                <td colspan="6" class="md-admin-table__empty">
                                    <i class="material-icons">receipt_long</i>
                                    <p>No orders found</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { mapState, mapMutations } from "vuex";
export default {
    name: 'Dashboard',

    data() {
        return {
            avaiableStatus: ["cancel", "confirmed", "preparing", "checking", "delivering", "delivered", "completed"],
            allBills: [],
            orders: [],
            showOrderDetails: false,
            sendId: undefined,
            interval: "",
        }
    },

    mounted: function () {
        this.getAllBills();

        // Check if admin is logged in
        if (!this.admin || this.admin.type !== 'admin') {
            console.log('Not authenticated as admin, redirecting to admin login');
            this.$router.push("/admin");
            return;
        }

        this.autoUpdate();
    },

    beforeUnmount() {
        clearInterval(this.interval)
    },

    computed: {
        ...mapState(["allFoods", "admin"]),

        filterBills: function () {
            return this.allBills.filter((b) => b.bill_status < 6 && b.bill_status > 0);
        },

        recentOrders: function () {
            // Get the 5 most recent orders, sorted by bill_id (newest first)
            return this.allBills
                .slice()
                .sort((a, b) => b.bill_id - a.bill_id)
                .slice(0, 5);
        },
    },

    methods: {
        ...mapMutations(["setAdmin"]),

        async getAllBills() {
            try {
                const response = await axios.get('/api/billstatus');
                this.allBills = response.data;

                // Debug logging
                console.log('Dashboard - Fetched bills:', this.allBills);
                console.log('Dashboard - Recent orders:', this.recentOrders);
            } catch (error) {
                console.error('Error fetching bills for dashboard:', error);
                this.allBills = [];
            }
        },

        sendBillId: function (id) {
            this.sendId = id
            this.showOrderDetails = !this.showOrderDetails;
        },

        closeView: function () {
            this.showOrderDetails = !this.showOrderDetails;
        },

        handleLogout: function () {
            this.setAdmin(null);
            this.$router.push("/admin");
        },

        async nextStatusBtn(id) {
            await axios.put('/billstatus/' + id);
            this.getAllBills();
        },

        async paidBtn(id) {
            await axios.put('/billstatus/paid/' + id);
            this.getAllBills();
        },

        async cancelBtn(id) {
            await axios.put('/billstatus/cancel/' + id);
            this.getAllBills();
        },

        autoUpdate: function () {
            this.interval = setInterval(function () {
                this.getAllBills();
            }.bind(this), 1000);
        },

        refreshData: function () {
            this.getAllBills();
        },

        viewDetails: function (id) {
            // Navigate to order management and highlight the specific order
            this.$router.push(`/admin/order-management?orderId=${id}`);
        },

        markAsPaid: function (id) {
            this.paidBtn(id);
        },

        cancelOrder: function (id) {
            this.cancelBtn(id);
        },

        getPendingOrders: function() {
            return this.allBills.filter(bill => bill.bill_status === 1).length || 0;
        },

        getCompletedOrders: function() {
            return this.allBills.filter(bill => bill.bill_status === 6).length || 0;
        },

        getTotalRevenue: function() {
            return this.allBills
                .filter(bill => bill.bill_status === 6)
                .reduce((total, bill) => total + parseFloat(bill.bill_total || 0), 0)
                .toFixed(2);
        },

        getOrderItemCount: function() {
            // For dashboard, show a simple indicator instead of exact count
            // to avoid multiple API calls. Exact count is available in order management.
            return "Multiple";
        },

        getStatusLabelFromBillStatus: function(billStatus) {
            const statusLabels = {
                0: 'Cancelled',
                1: 'Confirmed',
                2: 'Preparing',
                3: 'Checking',
                4: 'Delivering',
                5: 'Delivered',
                6: 'Completed'
            };
            return statusLabels[billStatus] || 'Unknown';
        },

        getStatusClassFromBillStatus: function(billStatus) {
            const statusMap = {
                0: 'md-status md-status--cancelled',
                1: 'md-status md-status--confirmed',
                2: 'md-status md-status--preparing',
                3: 'md-status md-status--checking',
                4: 'md-status md-status--delivering',
                5: 'md-status md-status--delivered',
                6: 'md-status md-status--completed'
            };
            return statusMap[billStatus] || 'md-status';
        },

        getStatusClass: function(status) {
            const statusMap = {
                'pending': 'md-status md-status--pending',
                'confirmed': 'md-status md-status--confirmed',
                'preparing': 'md-status md-status--preparing',
                'checking': 'md-status md-status--checking',
                'delivering': 'md-status md-status--delivering',
                'delivered': 'md-status md-status--delivered',
                'completed': 'md-status md-status--completed',
                'cancel': 'md-status md-status--cancelled'
            };
            return statusMap[status] || 'md-status';
        },

        navigateToUserManagement: function() {
            this.$router.push("/admin/user-management");
        },

        navigateToMenuManagement: function() {
            this.$router.push("/admin/menu-management");
        },

        navigateToOrderManagement: function() {
            this.$router.push("/admin/order-management");
        },

        navigateToReviewManagement: function() {
            this.$router.push("/admin/review-management");
        }
    },
}
</script>

<style>
/* Admin Dashboard Layout */
.md-admin-dashboard {
  padding: var(--md-sys-spacing-large);
  background-color: var(--md-sys-color-surface-variant);
  min-height: 100vh;
  color: var(--md-sys-color-on-surface);
}

/* Admin Header */
.md-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-large);
}

.md-admin-header__title-group {
  display: flex;
  flex-direction: column;
}

.md-admin-header__title {
  font-size: 2rem;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
  color: var(--md-sys-color-primary);
}

.md-admin-header__icon {
  margin-right: var(--md-sys-spacing-small);
  font-size: 2rem;
}

.md-admin-header__subtitle {
  font-size: 0.875rem;
  color: var(--md-sys-color-on-surface-variant);
  margin: 0.25rem 0 0 0;
}

.md-admin-header__actions {
  display: flex;
  gap: var(--md-sys-spacing-small);
}

/* Stats Cards */
.md-admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: var(--md-sys-spacing-medium);
  margin-bottom: var(--md-sys-spacing-large);
}

.md-stat-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-medium);
  box-shadow: var(--md-sys-elevation-1);
  padding: var(--md-sys-spacing-medium);
  display: flex;
  align-items: center;
  transition: box-shadow 0.3s ease;
}

.md-stat-card:hover {
  box-shadow: var(--md-sys-elevation-2);
}

.md-stat-card__icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--md-sys-spacing-medium);
}

.md-stat-card__icon i {
  font-size: 1.5rem;
  color: white;
}

.md-stat-card__icon--pending {
  background-color: var(--md-sys-color-tertiary);
}

.md-stat-card__icon--completed {
  background-color: var(--md-sys-color-secondary);
}

.md-stat-card__icon--revenue {
  background-color: var(--md-sys-color-primary);
}

.md-stat-card__content {
  flex: 1;
}

.md-stat-card__value {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: var(--md-sys-color-on-surface);
}

.md-stat-card__label {
  font-size: 0.875rem;
  margin: 0;
  color: var(--md-sys-color-on-surface-variant);
}

/* Card Styles */
.md-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-medium);
  box-shadow: var(--md-sys-elevation-1);
  overflow: hidden;
  margin-bottom: var(--md-sys-spacing-large);
}

.md-admin-orders__header {
  padding: var(--md-sys-spacing-medium);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-admin-orders__title {
  margin: 0;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface);
}

.md-admin-orders__title i {
  color: var(--md-sys-color-primary);
}

.md-admin-orders__content {
  padding: var(--md-sys-spacing-medium);
}

/* Admin Table */
.md-admin-table-container {
  width: 100%;
  overflow-x: auto;
}

.md-admin-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 750px;
}

.md-admin-table th {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  font-weight: 500;
  text-align: left;
  padding: 12px 16px;
  font-size: 0.875rem;
  white-space: nowrap;
}

.md-admin-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  font-size: 0.875rem;
  color: var(--md-sys-color-on-surface);
}

.md-admin-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.md-admin-table__actions {
  display: flex;
  gap: 8px;
  white-space: nowrap;
}

.md-admin-table__empty {
  text-align: center;
  padding: var(--md-sys-spacing-large) !important;
  color: var(--md-sys-color-on-surface-variant);
}

.md-admin-table__empty i {
  font-size: 3rem;
  margin-bottom: var(--md-sys-spacing-small);
  opacity: 0.5;
}

.md-admin-table__empty p {
  margin: 0;
  font-size: 1rem;
}

.md-order-id {
  font-weight: 500;
  color: var(--md-sys-color-primary);
}

.md-order-price {
  font-weight: 500;
}

/* Status indicators */
.md-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.md-status--pending {
  background-color: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
}

.md-status--confirmed {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.md-status--preparing {
  background-color: #FFF8E1;
  color: #FF8F00;
}

.md-status--checking {
  background-color: #E3F2FD;
  color: #1976D2;
}

.md-status--delivering {
  background-color: #E8F5E9;
  color: #388E3C;
}

.md-status--delivered {
  background-color: #E0F7FA;
  color: #00ACC1;
}

.md-status--completed {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.md-status--cancelled {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

/* Material Design buttons */
.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: var(--md-sys-shape-small);
  font-weight: 500;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.1px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
  gap: 8px;
}

.md-button--text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.md-button--text:hover {
  background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
}

.md-button--outlined {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-button--outlined:hover {
  background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
}

.md-button--outlined.md-button--warn {
  color: var(--md-sys-color-error);
}

.md-button--outlined.md-button--warn:hover {
  background-color: rgba(var(--md-sys-color-error-rgb), 0.08);
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-button--primary:hover {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  box-shadow: var(--md-sys-elevation-1);
}

.md-button--icon {
  width: 36px;
  height: 36px;
  padding: 0;
  min-width: 36px;
  border-radius: 18px;
  background-color: transparent;
  color: var(--md-sys-color-on-surface-variant);
}

.md-button--icon:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.md-button--icon.md-button--success {
  color: var(--md-sys-color-secondary);
}

.md-button--icon.md-button--danger {
  color: var(--md-sys-color-error);
}

/* Responsive styles */
@media (max-width: 768px) {
  .md-admin-dashboard {
    padding: var(--md-sys-spacing-medium);
  }

  .md-admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--md-sys-spacing-medium);
  }

  .md-admin-header__actions {
    width: 100%;
  }

  .md-admin-stats {
    grid-template-columns: 1fr;
  }
}
</style>