import {
    insertBillStatus,
    getNewestId,
    getBillsByBill
} from "../models/BillStatusModel.js";
import {
    insertBillDetails,
    getBillDetails
} from "../models/BillDetailsModel.js";
import { getAllItems as getCartByUser, deleteAllItemsByUser as clearCart } from "../models/CartModel.js";

// @desc    Create new order
// @route   POST /api/orders
// @access  Private (if authentication is implemented)
export const createOrder = (req, res) => {
    // 1. Extract data from request body
    // Assuming body contains: { userId, customerInfo, cartItems, totalPrice, paymentMethod, notes, ... }
    // Adjust this based on your actual frontend data structure
    const { userId, customerInfo, cartItems, totalPrice, paymentMethod = 'Cash', notes = '' } = req.body;

    // Basic validation (add more as needed)
    if (!userId || !cartItems || cartItems.length === 0) {
        return res.status(400).json({ message: 'User ID and cart items are required.' });
    }

    // 2. Create Bill Status data
    const billStatusData = {
        user_id: userId,
        bill_date: new Date(), // Or get from client if needed
        bill_total: totalPrice,
        bill_status: 1, // Assuming 1 means 'Pending' or 'Received'
        bill_paid: paymentMethod !== 'Cash', // Example logic
        bill_notes: notes, // Include customer name, phone, address here if needed
        // Add other fields like customer name, phone, address if they are in BillStatusModel
        // customer_name: customerInfo.name,
        // customer_phone: customerInfo.phone,
        // customer_address: customerInfo.address,
    };

    // 3. Insert Bill Status
    insertBillStatus(billStatusData, (err, statusResult) => {
        if (err) {
            console.error("Error inserting bill status:", err);
            return res.status(500).json({ message: "Could not create order status.", error: err });
        }

        // 4. Get the newly created bill_id
        // We need the ID to link Bill Details
        // This assumes insertBillStatus doesn't directly return the new ID
        getNewestId((err, newestIdResult) => {
            if (err || !newestIdResult || !newestIdResult.bill_id) {
                console.error("Error getting newest bill ID:", err);
                // Consider how to handle this error - maybe delete the created BillStatus?
                return res.status(500).json({ message: "Could not retrieve new order ID.", error: err });
            }

            const newBillId = newestIdResult.bill_id;

            // 5. Prepare and Insert Bill Details (multiple items)
            const detailPromises = cartItems.map(item => {
                return new Promise((resolve, reject) => {
                    const detailData = {
                        bill_id: newBillId,
                        food_id: item.food_id, // Assuming item has food_id
                        quantity: item.quantity,
                        price: item.price, // Assuming item has price
                        // Add other relevant fields from your Cart/Food model
                    };
                    insertBillDetails(detailData, (err, detailResult) => {
                        if (err) {
                            console.error("Error inserting bill detail:", err);
                            reject(err); // Reject the promise if insertion fails
                        } else {
                            resolve(detailResult); // Resolve the promise on success
                        }
                    });
                });
            });

            // 6. Wait for all details to be inserted
            Promise.all(detailPromises)
                .then(results => {
                    // 7. (Optional) Clear the user's cart after successful order
                    clearCart(userId, (err, clearResult) => {
                        if (err) {
                            console.warn("Could not clear cart for user:", userId, err);
                            // Don't necessarily fail the order, but log the warning
                        }
                    });

                    // 8. Send success response with the new bill_id
                    res.status(201).json({ 
                        message: "Order created successfully!", 
                        billId: newBillId 
                    });
                })
                .catch(error => {
                    // Handle errors from Promise.all (any failed detail insertion)
                    // Consider cleanup: delete the BillStatus and already inserted BillDetails
                    console.error("Error inserting one or more bill details:", error);
                    res.status(500).json({ message: "Could not save order details.", error: error });
                });
        });
    });
};

// @desc    Get order by ID (including details)
// @route   GET /api/orders/:id
// @access  Private (if needed)
export const getOrderDetails = (req, res) => {
    const billId = req.params.id;

    if (!billId) {
        return res.status(400).json({ message: 'Bill ID is required.' });
    }

    // Use Promise.all to fetch both status and details concurrently
    Promise.all([
        new Promise((resolve, reject) => {
            getBillsByBill(billId, (err, statusResult) => {
                if (err) return reject(err);
                if (!statusResult || statusResult.length === 0) return reject(new Error('Order not found'));
                resolve(statusResult[0]); // Assuming only one status per bill_id
            });
        }),
        new Promise((resolve, reject) => {
            getBillDetails(billId, (err, detailsResult) => {
                if (err) return reject(err);
                resolve(detailsResult || []); // Return empty array if no details
            });
        })
    ])
    .then(([status, details]) => {
        // Combine results and send response
        res.status(200).json({ 
            ...status, 
            items: details 
        });
    })
    .catch(error => {
        console.error(`Error fetching order details for bill ID ${billId}:`, error);
        if (error.message === 'Order not found') {
            res.status(404).json({ message: error.message });
        } else {
            res.status(500).json({ message: "Could not retrieve order details.", error: error.message });
        }
    });
};

// Add other handlers like getUserOrders if needed 