// <PERSON>ript to apply the reviews table fix
import mysql from 'mysql2';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create database connection
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'db_restaurant',
  multipleStatements: true // Important for running multiple SQL statements
});

// Read the SQL file
const sqlFilePath = path.join(__dirname, 'migrations', 'fix_reviews_table_simple.sql');
const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');

// Connect to the database
connection.connect(err => {
  if (err) {
    console.error('Error connecting to the database:', err);
    return;
  }
  console.log('Connected to the database successfully.');

  // Execute the SQL script
  connection.query(sqlScript, (err, results) => {
    if (err) {
      console.error('Error executing SQL script:', err);
    } else {
      console.log('Reviews table created successfully!');
    }

    // Close the connection
    connection.end();
  });
});
