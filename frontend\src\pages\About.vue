<template>
    <div class="md-about">
        <!-- Header Section with consistent styling -->
        <div class="md-section-header">
            <h1 class="md-section-header__title">About Us</h1>
            <p class="md-section-header__subtitle">Discover our story and passion for quality food</p>
        </div>

        <!-- Main content with card-like styling -->
        <div class="md-about__container">
            <!-- About content with chef image -->
            <div class="md-card md-about__content">
                <div class="md-about__image-container">
                    <img src="../assets/images/taco-chefcartoon.png" alt="Chef" class="md-about__chef-image">
                </div>
                <div class="md-about__text">
                    <h2 class="md-about__heading">Our Story</h2>
                    <p>Our restaurant FoodDeli was founded in 2002 in Vietnam by our 5 Michelin stars chef. After
                        that, thanks to the support of our customers, our brand has been popularized globally in markets
                        such as Australia, USA, Canada, UK, France, Germany, Belgium, Russia, China, Japan,
                        Singapore, and more.</p>
                    <p>We specialize in authentic Mexican-style meals, delivering only the highest quality products to our customers.</p>
                    <p>Customers can dine at our restaurant to experience the vibrant Mexican atmosphere or order food to be
                        delivered directly to their homes.</p>
                </div>
            </div>

            <!-- Motto section with icon -->
            <div class="md-card md-about__motto">
                <i class="material-icons md-about__motto-icon">restaurant</i>
                <h2 class="md-about__motto-text">Food Brings People Together</h2>
                <p>We believe in creating memorable dining experiences that connect people through delicious food.</p>
            </div>

            <!-- Gallery section with modern styling -->
            <h2 class="md-about__gallery-title">Our Food Gallery</h2>
            <div class="md-about__gallery">
                <div class="md-about__gallery-item">
                    <img src="../assets/images/taco/taco-2.jpg" alt="Taco">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/taco/taco-4.jpg" alt="Taco">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/burrito/burrito-6.jpg" alt="Burrito">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/burrito/burrito-2.jpg" alt="Burrito">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/burrito/burrito-3.jpg" alt="Burrito">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/nachos/nachos-1.jpg" alt="Nachos">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/nachos/nachos-2.jpg" alt="Nachos">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/nachos/nachos-3.jpg" alt="Nachos">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/dessert/dessert-2.jpg" alt="Dessert">
                </div>
                <div class="md-about__gallery-item">
                    <img src="../assets/images/dessert/dessert-6.jpg" alt="Dessert">
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "About",
};
</script>

<style scoped>
/* Base styles for About page that match Material Design principles */
.md-about {
  padding: var(--md-sys-spacing-medium);
  max-width: 1200px;
  margin: 0 auto;
  color: var(--md-sys-color-on-surface);
}

/* Header styles to match other pages */
.md-section-header {
  margin-bottom: var(--md-sys-spacing-large);
  text-align: center;
}

.md-section-header__title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--md-sys-spacing-small);
  color: var(--md-sys-color-primary);
}

.md-section-header__subtitle {
  font-size: 1.2rem;
  color: var(--md-sys-color-on-surface-variant);
  max-width: 600px;
  margin: 0 auto;
}

/* Container for all content */
.md-about__container {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-large);
}

/* Card styling to match rest of UI */
.md-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-medium);
  box-shadow: var(--md-sys-elevation-1);
  padding: var(--md-sys-spacing-medium);
  transition: box-shadow 0.3s ease;
}

.md-card:hover {
  box-shadow: var(--md-sys-elevation-2);
}

/* About content section */
.md-about__content {
  display: flex;
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-medium);
  align-items: center;
}

.md-about__image-container {
  flex: 1 1 300px;
  display: flex;
  justify-content: center;
}

.md-about__chef-image {
  max-width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
}

.md-about__text {
  flex: 2 1 400px;
}

.md-about__heading {
  font-size: 1.8rem;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-small);
}

.md-about__text p {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface);
}

/* Motto section */
.md-about__motto {
  text-align: center;
  padding: var(--md-sys-spacing-large);
}

.md-about__motto-icon {
  font-size: 3rem;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-about__motto-text {
  font-size: 1.8rem;
  text-transform: uppercase;
  margin-bottom: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface);
}

/* Gallery section */
.md-about__gallery-title {
  text-align: center;
  font-size: 1.8rem;
  margin-bottom: var(--md-sys-spacing-medium);
  color: var(--md-sys-color-primary);
}

.md-about__gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--md-sys-spacing-medium);
}

.md-about__gallery-item {
  border-radius: var(--md-sys-shape-small);
  overflow: hidden;
  box-shadow: var(--md-sys-elevation-1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.md-about__gallery-item:hover {
  transform: scale(1.03);
  box-shadow: var(--md-sys-elevation-2);
}

.md-about__gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

/* Responsive adjustments */
@media (min-width: 769px) {
  .md-about {
    padding: var(--md-sys-spacing-large);
  }
  
  .md-about__gallery {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 768px) {
  .md-section-header__title {
    font-size: 2rem;
  }
  
  .md-about__content {
    flex-direction: column;
  }
  
  .md-about__chef-image {
    margin-bottom: var(--md-sys-spacing-medium);
  }
}
</style>