import path from 'path';
import { fileURLToPath } from 'url';
import multer from 'multer';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../uploads/food'));
  },
  filename: (req, file, cb) => {
    // Create unique filename with original extension
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'food-' + uniqueSuffix + ext);
  }
});

// File filter to accept only images
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Not an image! Please upload only images.'), false);
  }
};

// Initialize upload
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: fileFilter
});

// Upload single file handler
export const uploadFoodImage = (req, res) => {
  // upload.single middleware is already handling the file
  // If we reach here, upload was successful
  if (!req.file) {
    return res.status(400).json({ 
      success: false, 
      message: 'No file uploaded' 
    });
  }
  
  // Return the file path that can be accessed via URL
  const relativePath = `/uploads/food/${req.file.filename}`;
  
  return res.json({
    success: true,
    file: {
      filename: req.file.filename,
      path: relativePath,
      fullUrl: `${req.protocol}://${req.get('host')}${relativePath}`
    }
  });
};

// Export the multer middleware for route usage
export const uploadMiddleware = upload.single('image');
