<template>
  <div class="md-admin-reviews">
    <!-- Header -->
    <div class="md-admin-header">
      <div class="md-admin-header__title-group">
        <h1 class="md-admin-header__title">
          <i class="material-icons md-admin-header__icon">rate_review</i>
          Review Management
        </h1>
        <p class="md-admin-header__subtitle">Monitor and manage customer reviews</p>
      </div>

      <div class="md-admin-header__actions">
        <button class="md-button md-button--text" @click="navigateToDashboard">
          <i class="material-icons">dashboard</i>
          <span>Dashboard</span>
        </button>
        <button class="md-button md-button--text" @click="refreshData">
          <i class="material-icons">refresh</i>
          <span>Refresh</span>
        </button>
        <button class="md-button md-button--text" @click="handleLogout">
          <i class="material-icons">logout</i>
          <span>Logout</span>
        </button>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="md-stats-grid" v-if="stats">
      <div class="md-stat-card">
        <div class="md-stat-card__icon md-stat-card__icon--reviews">
          <i class="material-icons">rate_review</i>
        </div>
        <div class="md-stat-card__content">
          <h3 class="md-stat-card__value">{{ stats.total_reviews || 0 }}</h3>
          <p class="md-stat-card__label">Total Reviews</p>
        </div>
      </div>

      <div class="md-stat-card">
        <div class="md-stat-card__icon md-stat-card__icon--rating">
          <i class="material-icons">star</i>
        </div>
        <div class="md-stat-card__content">
          <h3 class="md-stat-card__value">{{ parseFloat(stats.average_rating || 0).toFixed(1) }}</h3>
          <p class="md-stat-card__label">Average Rating</p>
        </div>
      </div>

      <div class="md-stat-card">
        <div class="md-stat-card__icon md-stat-card__icon--reported">
          <i class="material-icons">flag</i>
        </div>
        <div class="md-stat-card__content">
          <h3 class="md-stat-card__value">{{ stats.reported_reviews || 0 }}</h3>
          <p class="md-stat-card__label">Reported Reviews</p>
        </div>
      </div>

      <div class="md-stat-card">
        <div class="md-stat-card__icon md-stat-card__icon--today">
          <i class="material-icons">today</i>
        </div>
        <div class="md-stat-card__content">
          <h3 class="md-stat-card__value">{{ stats.today_reviews || 0 }}</h3>
          <p class="md-stat-card__label">Today's Reviews</p>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="md-card md-admin-filters">
      <div class="md-admin-filters__row">
        <div class="md-form-field">
          <label class="md-form-field__label">Filter by Rating</label>
          <select v-model="filters.rating" @change="loadReviews" class="md-form-field__select">
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>

        <div class="md-form-field">
          <label class="md-form-field__label">Filter by Status</label>
          <select v-model="filters.reported" @change="loadReviews" class="md-form-field__select">
            <option value="">All Reviews</option>
            <option value="true">Reported Only</option>
            <option value="false">Not Reported</option>
          </select>
        </div>

        <div class="md-form-field">
          <label class="md-form-field__label">Sort by</label>
          <select v-model="sortBy" @change="loadReviews" class="md-form-field__select">
            <option value="created_at">Date Created</option>
            <option value="rating">Rating</option>
            <option value="updated_at">Last Updated</option>
          </select>
        </div>

        <div class="md-form-field">
          <label class="md-form-field__label">Order</label>
          <select v-model="sortOrder" @change="loadReviews" class="md-form-field__select">
            <option value="DESC">Newest First</option>
            <option value="ASC">Oldest First</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Reviews Table -->
    <div class="md-card md-admin-reviews-table">
      <div class="md-admin-reviews-table__header">
        <h2 class="md-admin-reviews-table__title">
          <i class="material-icons">list</i>
          Reviews ({{ pagination.total || 0 }})
        </h2>
      </div>

      <div v-if="loading" class="md-admin-loading">
        <div class="md-spinner"></div>
        <p>Loading reviews...</p>
      </div>

      <div v-else-if="reviews.length === 0" class="md-admin-empty">
        <i class="material-icons">rate_review</i>
        <p>No reviews found</p>
      </div>

      <div v-else class="md-admin-table-container">
        <table class="md-admin-table">
          <thead>
            <tr>
              <th>Review</th>
              <th>Food Item</th>
              <th>Customer</th>
              <th>Rating</th>
              <th>Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="review in reviews" :key="review.review_id"
                :class="{'md-admin-table__row--reported': review.reported}">
              <td class="md-review-cell">
                <div class="md-review-preview">
                  <p class="md-review-preview__comment">{{ review.comment || 'No comment' }}</p>
                  <div v-if="review.admin_response" class="md-review-preview__response">
                    <i class="material-icons">reply</i>
                    <span>{{ review.admin_response }}</span>
                  </div>
                </div>
              </td>
              <td>
                <div class="md-food-info">
                  <img :src="getImagePath(review)" :alt="review.food_name" class="md-food-info__image"
                       @error="handleImageError($event, review)">
                  <span class="md-food-info__name">{{ review.food_name }}</span>
                </div>
              </td>
              <td>{{ review.user_name }}</td>
              <td>
                <div class="md-rating md-rating--small">
                  <span v-for="n in 5" :key="n" class="material-icons"
                        :class="{'md-rating__star--filled': n <= review.rating}">star</span>
                </div>
              </td>
              <td>{{ formatDate(review.created_at) }}</td>
              <td>
                <span v-if="review.reported" class="md-status md-status--reported">
                  <i class="material-icons">flag</i>
                  Reported
                </span>
                <span v-else-if="review.hidden" class="md-status md-status--hidden">
                  <i class="material-icons">visibility_off</i>
                  Hidden
                </span>
                <span v-else class="md-status md-status--active">
                  <i class="material-icons">visibility</i>
                  Active
                </span>
              </td>
              <td class="md-admin-table__actions">
                <button class="md-button md-button--text md-button--small"
                        @click="viewReviewDetails(review)">
                  <i class="material-icons">visibility</i>
                </button>
                <button v-if="!review.admin_response"
                        class="md-button md-button--text md-button--small"
                        @click="respondToReview(review)">
                  <i class="material-icons">reply</i>
                </button>
                <button v-if="review.reported"
                        class="md-button md-button--text md-button--small md-button--success"
                        @click="approveReview(review)">
                  <i class="material-icons">check</i>
                </button>
                <button class="md-button md-button--text md-button--small md-button--warning"
                        @click="hideReview(review)">
                  <i class="material-icons">visibility_off</i>
                </button>
                <button class="md-button md-button--text md-button--small md-button--danger"
                        @click="deleteReview(review)">
                  <i class="material-icons">delete</i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="pagination.totalPages > 1" class="md-pagination">
        <button class="md-button md-button--text"
                :disabled="pagination.page <= 1"
                @click="changePage(pagination.page - 1)">
          <i class="material-icons">chevron_left</i>
          Previous
        </button>

        <span class="md-pagination__info">
          Page {{ pagination.page }} of {{ pagination.totalPages }}
        </span>

        <button class="md-button md-button--text"
                :disabled="pagination.page >= pagination.totalPages"
                @click="changePage(pagination.page + 1)">
          Next
          <i class="material-icons">chevron_right</i>
        </button>
      </div>
    </div>

    <!-- Review Details Modal -->
    <div v-if="showDetailsModal" class="md-dialog-overlay" @click.self="showDetailsModal = false">
      <div class="md-dialog md-dialog--large">
        <div class="md-dialog__header">
          <h2 class="md-dialog__title">Review Details</h2>
          <button @click="showDetailsModal = false" class="md-dialog__close">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="md-dialog__content">
          <div v-if="selectedReview" class="md-review-details">
            <div class="md-review-details__header">
              <div class="md-food-info md-food-info--large">
                <img :src="getImagePath(selectedReview)" :alt="selectedReview.food_name" class="md-food-info__image"
                     @error="handleImageError($event, selectedReview)">
                <div class="md-food-info__content">
                  <h3 class="md-food-info__name">{{ selectedReview.food_name }}</h3>
                  <div class="md-rating">
                    <span v-for="n in 5" :key="n" class="material-icons"
                          :class="{'md-rating__star--filled': n <= selectedReview.rating}">star</span>
                  </div>
                </div>
              </div>
              <div class="md-review-details__meta">
                <p><strong>Customer:</strong> {{ selectedReview.user_name }}</p>
                <p><strong>Date:</strong> {{ formatDate(selectedReview.created_at) }}</p>
                <p><strong>Status:</strong>
                  <span v-if="selectedReview.reported" class="md-status md-status--reported">Reported</span>
                  <span v-else-if="selectedReview.hidden" class="md-status md-status--hidden">Hidden</span>
                  <span v-else class="md-status md-status--active">Active</span>
                </p>
              </div>
            </div>

            <div class="md-review-details__content">
              <h4>Customer Review:</h4>
              <p class="md-review-details__comment">{{ selectedReview.comment || 'No comment provided' }}</p>

              <div v-if="selectedReview.reported" class="md-review-details__report">
                <h4>Report Information:</h4>
                <p><strong>Reason:</strong> {{ selectedReview.report_reason }}</p>
              </div>

              <div v-if="selectedReview.admin_response" class="md-review-details__response">
                <h4>Admin Response:</h4>
                <p>{{ selectedReview.admin_response }}</p>
                <small>Responded on: {{ formatDate(selectedReview.response_date) }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Response Modal -->
    <div v-if="showResponseModal" class="md-dialog-overlay" @click.self="showResponseModal = false">
      <div class="md-dialog">
        <div class="md-dialog__header">
          <h2 class="md-dialog__title">Respond to Review</h2>
          <button @click="showResponseModal = false" class="md-dialog__close">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="md-dialog__content">
          <div v-if="selectedReview" class="md-response-form">
            <div class="md-review-summary">
              <h4>Responding to:</h4>
              <div class="md-review-summary__content">
                <div class="md-rating md-rating--small">
                  <span v-for="n in 5" :key="n" class="material-icons"
                        :class="{'md-rating__star--filled': n <= selectedReview.rating}">star</span>
                </div>
                <p>{{ selectedReview.comment || 'No comment' }}</p>
                <small>by {{ selectedReview.user_name }} on {{ formatDate(selectedReview.created_at) }}</small>
              </div>
            </div>

            <div class="md-form-field">
              <label class="md-form-field__label">Your Response</label>
              <textarea
                v-model="responseText"
                class="md-form-field__textarea"
                placeholder="Write a professional response to this review..."
                rows="4"
                required
              ></textarea>
            </div>
          </div>
        </div>
        <div class="md-dialog__actions">
          <button class="md-button md-button--text" @click="showResponseModal = false">
            Cancel
          </button>
          <button class="md-button md-button--primary" @click="submitResponse" :disabled="!responseText.trim()">
            <i class="material-icons">send</i>
            Send Response
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import axios from "axios";

export default {
  name: 'ReviewManagement',

  data() {
    return {
      reviews: [],
      stats: null,
      loading: false,
      showDetailsModal: false,
      showResponseModal: false,
      selectedReview: null,
      responseText: '',
      filters: {
        rating: '',
        reported: ''
      },
      sortBy: 'created_at',
      sortOrder: 'DESC',
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0
      }
    };
  },

  computed: {
    ...mapState(["admin"]),
  },

  mounted() {
    // Check if admin is logged in
    if (!this.admin || this.admin.type !== 'admin') {
      console.log('Not authenticated as admin, redirecting to admin login');
      this.$router.push("/admin");
      return;
    }

    this.loadStats();
    this.loadReviews();
  },

  methods: {
    ...mapMutations(["setAdmin"]),

    async loadStats() {
      try {
        const response = await axios.get('/api/admin/reviews/stats');
        this.stats = response.data;
      } catch (error) {
        console.error('Error loading review stats:', error);
      }
    },

    async loadReviews() {
      try {
        this.loading = true;
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit,
          sortBy: this.sortBy,
          sortOrder: this.sortOrder,
          ...this.filters
        };

        const response = await axios.get('/api/admin/reviews', { params });
        this.reviews = response.data.reviews;
        this.pagination = {
          page: response.data.page,
          limit: response.data.limit,
          total: response.data.total,
          totalPages: response.data.totalPages
        };
      } catch (error) {
        console.error('Error loading reviews:', error);
      } finally {
        this.loading = false;
      }
    },

    changePage(page) {
      this.pagination.page = page;
      this.loadReviews();
    },

    refreshData() {
      this.loadStats();
      this.loadReviews();
    },

    navigateToDashboard() {
      this.$router.push('/admin/dashboard');
    },

    handleLogout() {
      this.setAdmin(null);
      this.$router.push("/admin");
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    viewReviewDetails(review) {
      this.selectedReview = review;
      this.showDetailsModal = true;
    },

    respondToReview(review) {
      this.selectedReview = review;
      this.responseText = '';
      this.showResponseModal = true;
    },

    async approveReview(review) {
      try {
        await axios.put(`/api/admin/reviews/${review.review_id}/status`, {
          action: 'approve',
          admin_notes: 'Review approved by admin'
        });
        this.loadReviews();
        this.loadStats();
      } catch (error) {
        console.error('Error approving review:', error);
      }
    },

    async hideReview(review) {
      try {
        await axios.put(`/api/admin/reviews/${review.review_id}/status`, {
          action: 'hide',
          admin_notes: 'Review hidden by admin'
        });
        this.loadReviews();
        this.loadStats();
      } catch (error) {
        console.error('Error hiding review:', error);
      }
    },

    async deleteReview(review) {
      if (confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
        try {
          await axios.put(`/api/admin/reviews/${review.review_id}/status`, {
            action: 'delete'
          });
          this.loadReviews();
          this.loadStats();
        } catch (error) {
          console.error('Error deleting review:', error);
        }
      }
    },

    async submitResponse() {
      if (!this.responseText.trim()) return;

      try {
        await axios.post(`/api/admin/reviews/${this.selectedReview.review_id}/response`, {
          response: this.responseText,
          admin_id: this.admin.userId || 1 // Use admin ID from store or default
        });

        this.showResponseModal = false;
        this.responseText = '';
        this.loadReviews();
      } catch (error) {
        console.error('Error submitting response:', error);
        alert('Error submitting response. Please try again.');
      }
    },

    // Get image path for food items (same logic as MenuManagement)
    getImagePath(review) {
      // First, handle null/undefined data gracefully
      if (!review) {
        return require('@/assets/images/nachos-img.png');
      }

      // Handle image path formats from both API and database
      const imageName = review.food_src || review.food_image || review.image;

      // If it's a full URL, use it directly
      if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
        return imageName;
      }

      // Handle cases where database returns 'food-img.png' which doesn't exist
      if (imageName === 'food-img.png') {
        // Get category-based default instead
        const category = (review.food_category || review.category || 'other').toLowerCase();
        return this.getCategoryDefaultImage(category);
      }

      // Use a safer approach with a default fallback image
      if (imageName) {
        try {
          return require(`@/assets/images/${imageName}`);
        } catch (error) {
          // Image not found, will select category-based default
          console.log('Image not found:', imageName);
        }
      }

      // Select a default image based on category
      const category = (review.food_category || review.category || 'other').toLowerCase();
      return this.getCategoryDefaultImage(category);
    },

    // Helper method to get default images by category
    getCategoryDefaultImage(category) {
      // These are guaranteed to exist in our assets
      if (category.includes('taco')) {
        return require('@/assets/images/taco-img.png');
      } else if (category.includes('burrito')) {
        return require('@/assets/images/burrito-img.png');
      } else if (category.includes('nacho')) {
        return require('@/assets/images/nachos-img.png');
      } else if (category.includes('side') || category.includes('salad')) {
        return require('@/assets/images/salad-img.png');
      } else if (category.includes('dessert')) {
        return require('@/assets/images/dessert-img.png');
      } else if (category.includes('coca') || category.includes('drink')) {
        return require('@/assets/images/coca-img.png');
      }

      // Default fallback image for any other category
      return require('@/assets/images/nachos-img.png');
    },

    // Handle image error in list view
    handleImageError(event, review) {
      console.log('Image load error for review:', review.food_name, 'trying fallback');
      const category = (review.food_category || review.category || 'other').toLowerCase();
      event.target.src = this.getCategoryDefaultImage(category);
    }
  }
};
</script>

<style scoped>
.md-admin-reviews {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.md-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.md-admin-header__title-group h1 {
  margin: 0;
  color: #1a1a1a;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.md-admin-header__subtitle {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 16px;
}

.md-admin-header__actions {
  display: flex;
  gap: 12px;
}

.md-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.md-stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.md-stat-card__icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.md-stat-card__icon--reviews { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.md-stat-card__icon--rating { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.md-stat-card__icon--reported { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
.md-stat-card__icon--today { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

.md-stat-card__value {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
}

.md-stat-card__label {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

.md-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.md-admin-filters {
  padding: 24px;
}

.md-admin-filters__row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.md-form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.md-form-field__label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.md-form-field__select,
.md-form-field__textarea {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.md-form-field__select:focus,
.md-form-field__textarea:focus {
  outline: none;
  border-color: #ff6b35;
}

.md-form-field__textarea {
  resize: vertical;
  min-height: 100px;
}

.md-admin-reviews-table {
  padding: 0;
}

.md-admin-reviews-table__header {
  padding: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.md-admin-reviews-table__title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.md-admin-loading,
.md-admin-empty {
  padding: 48px;
  text-align: center;
  color: #666;
}

.md-admin-loading .md-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.md-admin-empty i {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.md-admin-table-container {
  overflow-x: auto;
}

.md-admin-table {
  width: 100%;
  border-collapse: collapse;
}

.md-admin-table th,
.md-admin-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.md-admin-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.md-admin-table__row--reported {
  background-color: #fff3e0;
}

.md-review-cell {
  max-width: 300px;
}

.md-review-preview__comment {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.md-review-preview__response {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.md-food-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.md-food-info__image {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.md-food-info__name {
  font-weight: 500;
  font-size: 14px;
}

.md-food-info--large .md-food-info__image {
  width: 60px;
  height: 60px;
}

.md-rating {
  display: flex;
  gap: 2px;
}

.md-rating--small .material-icons {
  font-size: 16px;
}

.md-rating__star--filled {
  color: #ffc107;
}

.md-status {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.md-status--reported {
  background-color: #ffebee;
  color: #c62828;
}

.md-status--hidden {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.md-status--active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.md-admin-table__actions {
  display: flex;
  gap: 4px;
}

.md-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.md-button--text {
  background: transparent;
  color: #666;
}

.md-button--text:hover {
  background-color: #f5f5f5;
  color: #333;
}

.md-button--small {
  padding: 6px 12px;
  font-size: 12px;
}

.md-button--primary {
  background-color: #ff6b35;
  color: white;
}

.md-button--primary:hover {
  background-color: #e55a2b;
}

.md-button--success {
  color: #2e7d32;
}

.md-button--success:hover {
  background-color: #e8f5e8;
}

.md-button--warning {
  color: #f57c00;
}

.md-button--warning:hover {
  background-color: #fff3e0;
}

.md-button--danger {
  color: #d32f2f;
}

.md-button--danger:hover {
  background-color: #ffebee;
}

.md-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.md-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-top: 1px solid #e0e0e0;
}

.md-pagination__info {
  font-size: 14px;
  color: #666;
}

.md-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.md-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.md-dialog--large {
  max-width: 800px;
}

.md-dialog__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.md-dialog__title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.md-dialog__close {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  color: #666;
  transition: background-color 0.2s;
}

.md-dialog__close:hover {
  background-color: #f5f5f5;
}

.md-dialog__content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.md-dialog__actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e0e0e0;
}

.md-review-details__header {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.md-review-details__meta p {
  margin: 8px 0;
  font-size: 14px;
}

.md-review-details__content h4 {
  margin: 24px 0 12px 0;
  color: #333;
  font-size: 16px;
}

.md-review-details__comment {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin: 0;
  line-height: 1.5;
}

.md-review-details__report {
  background-color: #fff3e0;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.md-review-details__response {
  background-color: #e8f5e8;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #4caf50;
}

.md-response-form .md-review-summary {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.md-response-form .md-review-summary h4 {
  margin: 0 0 12px 0;
  color: #333;
}

.md-response-form .md-review-summary__content p {
  margin: 8px 0;
  font-style: italic;
}

.md-response-form .md-review-summary__content small {
  color: #666;
}

@media (max-width: 768px) {
  .md-admin-reviews {
    padding: 16px;
  }

  .md-admin-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .md-stats-grid {
    grid-template-columns: 1fr;
  }

  .md-admin-filters__row {
    grid-template-columns: 1fr;
  }

  .md-admin-table-container {
    font-size: 14px;
  }

  .md-admin-table th,
  .md-admin-table td {
    padding: 12px 8px;
  }

  .md-dialog {
    width: 95%;
    margin: 16px;
  }

  .md-review-details__header {
    flex-direction: column;
  }
}
</style>
