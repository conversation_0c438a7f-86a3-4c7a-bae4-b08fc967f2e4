<template>
  <div class="md-my-reviews">
    <div class="md-container">
      <!-- Header -->
      <div class="md-page-header">
        <h1 class="md-page-header__title">
          <i class="material-icons">rate_review</i>
          My Reviews
        </h1>
        <p class="md-page-header__subtitle">Manage your food reviews and ratings</p>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="md-loading">
        <div class="md-spinner"></div>
        <p>Loading your reviews...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="reviews.length === 0" class="md-empty-state">
        <i class="material-icons">rate_review</i>
        <h3>No Reviews Yet</h3>
        <p>You haven't written any reviews yet. Order some food and share your experience!</p>
        <router-link to="/menu" class="md-button md-button--primary">
          <i class="material-icons">restaurant_menu</i>
          Browse Menu
        </router-link>
      </div>

      <!-- Reviews List -->
      <div v-else class="md-reviews-list">
        <div v-for="review in reviews" :key="review.review_id" class="md-review-card">
          <div class="md-review-card__header">
            <div class="md-food-info">
              <img :src="getImagePath(review)" :alt="review.food_name" class="md-food-info__image"
                   @error="handleImageError($event, review)">
              <div class="md-food-info__content">
                <h3 class="md-food-info__name">{{ review.food_name }}</h3>
                <div class="md-rating">
                  <span v-for="n in 5" :key="n" class="material-icons"
                        :class="{'md-rating__star--filled': n <= review.rating}">star</span>
                </div>
              </div>
            </div>
            <div class="md-review-card__meta">
              <span class="md-review-card__date">{{ formatDate(review.created_at) }}</span>
              <div class="md-review-card__actions">
                <button class="md-button md-button--text md-button--small" @click="editReview(review)">
                  <i class="material-icons">edit</i>
                  Edit
                </button>
                <button class="md-button md-button--text md-button--small md-button--danger"
                        @click="deleteReview(review)">
                  <i class="material-icons">delete</i>
                  Delete
                </button>
              </div>
            </div>
          </div>

          <div class="md-review-card__content">
            <p class="md-review-card__comment">{{ review.comment || 'No comment provided' }}</p>

            <!-- Admin Response -->
            <div v-if="review.admin_response" class="md-review-card__response">
              <div class="md-review-card__response-header">
                <i class="material-icons">reply</i>
                <span>Restaurant Response</span>
              </div>
              <p class="md-review-card__response-text">{{ review.admin_response }}</p>
              <small class="md-review-card__response-date">
                {{ formatDate(review.response_date) }}
              </small>
            </div>
          </div>
        </div>
      </div>

      <!-- Edit Review Modal -->
      <div v-if="showEditModal" class="md-dialog-overlay" @click.self="showEditModal = false">
        <div class="md-dialog">
          <div class="md-dialog__header">
            <h2 class="md-dialog__title">Edit Review</h2>
            <button @click="showEditModal = false" class="md-dialog__close">
              <i class="material-icons">close</i>
            </button>
          </div>
          <div class="md-dialog__content">
            <div v-if="editingReview" class="md-edit-review-form">
              <div class="md-food-summary">
                <img :src="getImagePath(editingReview)" :alt="editingReview.food_name"
                     class="md-food-summary__image" @error="handleImageError($event, editingReview)">
                <h3 class="md-food-summary__name">{{ editingReview.food_name }}</h3>
              </div>

              <div class="md-form-field">
                <label class="md-form-field__label">Rating</label>
                <div class="md-rating-selector">
                  <span v-for="star in 5" :key="star"
                        class="material-icons md-rating-selector__star"
                        :class="{'md-rating-selector__star--selected': star <= editForm.rating}"
                        @click="editForm.rating = star">
                    {{ star <= editForm.rating ? 'star' : 'star_border' }}
                  </span>
                </div>
              </div>

              <div class="md-form-field">
                <label class="md-form-field__label">Comment</label>
                <textarea v-model="editForm.comment"
                          class="md-form-field__textarea"
                          placeholder="Share your experience with this dish..."
                          rows="4"></textarea>
              </div>
            </div>
          </div>
          <div class="md-dialog__actions">
            <button class="md-button md-button--text" @click="showEditModal = false">
              Cancel
            </button>
            <button class="md-button md-button--primary" @click="updateReview" :disabled="!editForm.rating">
              <i class="material-icons">save</i>
              Update Review
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import axios from "axios";

export default {
  name: 'MyReviews',

  data() {
    return {
      reviews: [],
      loading: false,
      showEditModal: false,
      editingReview: null,
      editForm: {
        rating: 0,
        comment: ''
      }
    };
  },

  computed: {
    ...mapState(["user"]),
  },

  mounted() {
    if (!this.user) {
      this.$router.push('/login');
      return;
    }
    this.loadReviews();
  },

  methods: {
    async loadReviews() {
      try {
        this.loading = true;
        const response = await axios.get(`/api/users/${this.user.user_id}/reviews`);
        this.reviews = response.data;
      } catch (error) {
        console.error('Error loading reviews:', error);
      } finally {
        this.loading = false;
      }
    },

    editReview(review) {
      this.editingReview = review;
      this.editForm = {
        rating: review.rating,
        comment: review.comment || ''
      };
      this.showEditModal = true;
    },

    async updateReview() {
      try {
        await axios.put(`/api/reviews/${this.editingReview.review_id}`, this.editForm);
        this.showEditModal = false;
        this.loadReviews();
      } catch (error) {
        console.error('Error updating review:', error);
        alert('Error updating review. Please try again.');
      }
    },

    async deleteReview(review) {
      if (confirm('Are you sure you want to delete this review?')) {
        try {
          await axios.delete(`/api/reviews/${review.review_id}`);
          this.loadReviews();
        } catch (error) {
          console.error('Error deleting review:', error);
          alert('Error deleting review. Please try again.');
        }
      }
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },

    // Get image path for food items (same logic as MenuManagement)
    getImagePath(review) {
      // First, handle null/undefined data gracefully
      if (!review) {
        return require('@/assets/images/nachos-img.png');
      }

      // Handle image path formats from both API and database
      const imageName = review.food_src || review.food_image || review.image;

      // If it's a full URL, use it directly
      if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
        return imageName;
      }

      // Handle cases where database returns 'food-img.png' which doesn't exist
      if (imageName === 'food-img.png') {
        // Get category-based default instead
        const category = (review.food_category || review.category || 'other').toLowerCase();
        return this.getCategoryDefaultImage(category);
      }

      // Use a safer approach with a default fallback image
      if (imageName) {
        try {
          return require(`@/assets/images/${imageName}`);
        } catch (error) {
          // Image not found, will select category-based default
          console.log('Image not found:', imageName);
        }
      }

      // Select a default image based on category
      const category = (review.food_category || review.category || 'other').toLowerCase();
      return this.getCategoryDefaultImage(category);
    },

    // Helper method to get default images by category
    getCategoryDefaultImage(category) {
      // These are guaranteed to exist in our assets
      if (category.includes('taco')) {
        return require('@/assets/images/taco-img.png');
      } else if (category.includes('burrito')) {
        return require('@/assets/images/burrito-img.png');
      } else if (category.includes('nacho')) {
        return require('@/assets/images/nachos-img.png');
      } else if (category.includes('side') || category.includes('salad')) {
        return require('@/assets/images/salad-img.png');
      } else if (category.includes('dessert')) {
        return require('@/assets/images/dessert-img.png');
      } else if (category.includes('coca') || category.includes('drink')) {
        return require('@/assets/images/coca-img.png');
      }

      // Default fallback image for any other category
      return require('@/assets/images/nachos-img.png');
    },

    // Handle image error in list view
    handleImageError(event, review) {
      console.log('Image load error for review:', review.food_name, 'trying fallback');
      const category = (review.food_category || review.category || 'other').toLowerCase();
      event.target.src = this.getCategoryDefaultImage(category);
    }
  }
};
</script>

<style scoped>
.md-my-reviews {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 24px 0;
}

.md-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
}

.md-page-header {
  text-align: center;
  margin-bottom: 32px;
}

.md-page-header__title {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.md-page-header__subtitle {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.md-loading,
.md-empty-state {
  text-align: center;
  padding: 48px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.md-loading .md-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.md-empty-state i {
  font-size: 64px;
  color: #ccc;
  margin-bottom: 16px;
}

.md-empty-state h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.md-empty-state p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

.md-reviews-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.md-review-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.md-review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.md-review-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.md-food-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.md-food-info__image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.md-food-info__name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.md-rating {
  display: flex;
  gap: 2px;
}

.md-rating__star--filled {
  color: #ffc107;
}

.md-review-card__meta {
  text-align: right;
}

.md-review-card__date {
  display: block;
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.md-review-card__actions {
  display: flex;
  gap: 8px;
}

.md-review-card__content {
  padding: 20px;
}

.md-review-card__comment {
  margin: 0 0 16px 0;
  line-height: 1.6;
  color: #333;
}

.md-review-card__response {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ff6b35;
}

.md-review-card__response-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #ff6b35;
  font-size: 14px;
}

.md-review-card__response-text {
  margin: 0 0 8px 0;
  line-height: 1.5;
  color: #333;
}

.md-review-card__response-date {
  color: #666;
  font-size: 12px;
}

.md-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.md-button--text {
  background: transparent;
  color: #666;
}

.md-button--text:hover {
  background-color: #f5f5f5;
  color: #333;
}

.md-button--small {
  padding: 6px 12px;
  font-size: 12px;
}

.md-button--primary {
  background-color: #ff6b35;
  color: white;
}

.md-button--primary:hover {
  background-color: #e55a2b;
}

.md-button--danger {
  color: #d32f2f;
}

.md-button--danger:hover {
  background-color: #ffebee;
}

.md-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.md-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.md-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.md-dialog__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.md-dialog__title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.md-dialog__close {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  color: #666;
  transition: background-color 0.2s;
}

.md-dialog__close:hover {
  background-color: #f5f5f5;
}

.md-dialog__content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.md-dialog__actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e0e0e0;
}

.md-food-summary {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.md-food-summary__image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.md-food-summary__name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.md-form-field {
  margin-bottom: 20px;
}

.md-form-field__label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.md-form-field__textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
}

.md-form-field__textarea:focus {
  outline: none;
  border-color: #ff6b35;
}

.md-rating-selector {
  display: flex;
  gap: 4px;
}

.md-rating-selector__star {
  font-size: 32px;
  cursor: pointer;
  color: #ddd;
  transition: color 0.2s;
}

.md-rating-selector__star:hover,
.md-rating-selector__star--selected {
  color: #ffc107;
}

@media (max-width: 768px) {
  .md-my-reviews {
    padding: 16px 0;
  }

  .md-page-header__title {
    font-size: 24px;
  }

  .md-review-card__header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .md-review-card__meta {
    text-align: left;
    width: 100%;
  }

  .md-review-card__actions {
    justify-content: flex-end;
    width: 100%;
  }

  .md-dialog {
    width: 95%;
    margin: 16px;
  }

  .md-food-summary {
    flex-direction: column;
    text-align: center;
  }
}
</style>
