// Import models
import * as ReviewModel from "../models/ReviewModel.js";

// Get all reviews for a specific food
export const getReviewsByFood = (req, res) => {
    const foodId = req.params.foodId;
    // Get reviews for a specific food item
    ReviewModel.getReviewsByFoodId(foodId, (err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving reviews",
                error: err
            });
        } else {
            res.status(200).json(results);
        }
    });
};

// Get a specific user's review for a food item
export const getUserReviewForFood = (req, res) => {
    const userId = req.params.userId;
    const foodId = req.params.foodId;

    ReviewModel.getReviewByUserAndFood(userId, foodId, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving user review",
                error: err
            });
        } else {
            res.status(200).json(result);
        }
    });
};

// Add a new review
export const createReview = (req, res) => {
    const review = req.body;

    // Validate input
    if (!review.user_id || !review.food_id || !review.rating) {
        return res.status(400).json({
            message: "Review must include user_id, food_id, and rating"
        });
    }

    if (review.rating < 1 || review.rating > 5) {
        return res.status(400).json({
            message: "Rating must be between 1 and 5"
        });
    }

    // Check if user has already reviewed this food
    ReviewModel.getReviewByUserAndFood(review.user_id, review.food_id, (err, existingReview) => {
        if (err) {
            return res.status(500).json({
                message: "Error checking existing review",
                error: err
            });
        }

        if (existingReview) {
            return res.status(400).json({
                message: "You have already reviewed this food item",
                existingReview: existingReview
            });
        }

        // Create new review
        ReviewModel.addReview(review, (err, result) => {
            if (err) {
                res.status(500).json({
                    message: "Error creating review",
                    error: err
                });
            } else {
                res.status(201).json({
                    message: "Review added successfully",
                    reviewId: result.insertId
                });
            }
        });
    });
};

// Update an existing review
export const updateReview = (req, res) => {
    const reviewId = req.params.reviewId;
    const review = req.body;

    // Validate input
    if (!review.rating) {
        return res.status(400).json({
            message: "Review must include rating"
        });
    }

    if (review.rating < 1 || review.rating > 5) {
        return res.status(400).json({
            message: "Rating must be between 1 and 5"
        });
    }

    ReviewModel.updateReview(review, reviewId, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error updating review",
                error: err
            });
        } else {
            res.status(200).json({
                message: "Review updated successfully"
            });
        }
    });
};

// Delete a review
export const deleteReview = (req, res) => {
    const reviewId = req.params.reviewId;

    ReviewModel.deleteReview(reviewId, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error deleting review",
                error: err
            });
        } else {
            res.status(200).json({
                message: "Review deleted successfully"
            });
        }
    });
};

// Report a review
export const reportReview = (req, res) => {
    const reviewId = req.params.reviewId;
    const { reason } = req.body;

    if (!reason) {
        return res.status(400).json({
            message: "Report reason is required"
        });
    }

    ReviewModel.reportReview(reviewId, reason, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error reporting review",
                error: err
            });
        } else {
            res.status(200).json({
                message: "Review reported successfully"
            });
        }
    });
};

// Get all user's reviews
export const getUserReviews = (req, res) => {
    const userId = req.params.userId;

    ReviewModel.getUserReviews(userId, (err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving user reviews",
                error: err
            });
        } else {
            res.status(200).json(results);
        }
    });
};

// Get all reported reviews (for admin)
export const getReportedReviews = (req, res) => {
    ReviewModel.getReportedReviews((err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving reported reviews",
                error: err
            });
        } else {
            res.status(200).json(results);
        }
    });
};

// Get review statistics for admin dashboard
export const getReviewStats = (req, res) => {
    ReviewModel.getReviewStatistics((err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving review statistics",
                error: err
            });
        } else {
            res.status(200).json(results);
        }
    });
};

// Get all reviews for admin management
export const getAllReviewsForAdmin = (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder || 'DESC';
    const filterRating = req.query.rating;
    const filterReported = req.query.reported;

    ReviewModel.getAllReviewsWithPagination(page, limit, sortBy, sortOrder, filterRating, filterReported, (err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving reviews for admin",
                error: err
            });
        } else {
            res.status(200).json(results);
        }
    });
};

// Admin response to review
export const addAdminResponse = (req, res) => {
    const reviewId = req.params.reviewId;
    const { response, admin_id } = req.body;

    if (!response) {
        return res.status(400).json({
            message: "Response content is required"
        });
    }

    ReviewModel.addAdminResponse(reviewId, response, admin_id, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error adding admin response",
                error: err
            });
        } else {
            res.status(201).json({
                message: "Admin response added successfully",
                responseId: result.insertId
            });
        }
    });
};

// Update review status (approve/reject reported reviews)
export const updateReviewStatus = (req, res) => {
    const reviewId = req.params.reviewId;
    const { action, admin_notes } = req.body; // action: 'approve', 'hide', 'delete'

    if (!action || !['approve', 'hide', 'delete'].includes(action)) {
        return res.status(400).json({
            message: "Valid action is required (approve, hide, delete)"
        });
    }

    ReviewModel.updateReviewStatus(reviewId, action, admin_notes, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error updating review status",
                error: err
            });
        } else {
            res.status(200).json({
                message: `Review ${action}d successfully`
            });
        }
    });
};
