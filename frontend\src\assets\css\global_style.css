@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;300;400;500;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Satisfy&display=swap");

* {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    outline: none;
    border: none;
    text-decoration: none;
    text-transform: capitalize;
    transition: .2s linear;
}

a {
    text-decoration: none !important;
}

html {
    font-size: 62.5%;
    overflow-x: hidden;
    scroll-behavior: smooth;
    scroll-padding-top: 8.5rem;
}

html::-webkit-scrollbar {
    width: 1rem;
}

html::-webkit-scrollbar-track {
    background: #fff;
}

html::-webkit-scrollbar-thumb {
    background: #27ae60;
    border-radius: 5rem;
}

.heading {
    text-align: center;
    padding-bottom: 2rem;
}

.heading span {
    font-family: 'Satisfy', cursive;
    font-size: 3rem;
    color: #27ae60;
}

.heading h3 {
    font-size: 3rem;
    color: #130f40;
}

.btn {
    padding: .7rem 1.8rem;
    font-size: 1.7rem;
    cursor: pointer;
    color: #fff;
    background: #27ae60;
    border-radius: .5rem;
}

.btn:hover {
    background-color: #f38609;
    color: #fff;
}

.btn:disabled {
    background-color: #130f40 !important;
    color: #fff;
}

@media (max-width: 768px) {
    html {
        font-size: 55%;
    }
}

@media (max-width: 576px) {
    html {
        font-size: 50%;
    }
}