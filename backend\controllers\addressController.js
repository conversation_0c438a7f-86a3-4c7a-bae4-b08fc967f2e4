import * as AddressModel from "../models/AddressModel.js";

// Get all addresses for a user
export const getUserAddresses = (req, res) => {
    const userId = req.params.userId;

    AddressModel.getUserAddresses(userId, (err, results) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving user addresses",
                error: err
            });
        } else {
            res.status(200).json(results);
        }
    });
};

// Get default address for a user
export const getUserDefaultAddress = (req, res) => {
    const userId = req.params.userId;

    AddressModel.getUserDefaultAddress(userId, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error retrieving default address",
                error: err
            });
        } else if (!result) {
            res.status(404).json({
                message: "No default address found"
            });
        } else {
            res.status(200).json(result);
        }
    });
};

// Get address by ID
export const getAddressById = (req, res) => {
    const addressId = req.params.addressId;
    const userId = req.user?.user_id || req.params.userId; // From auth middleware or params

    // First check if user owns this address
    AddressModel.checkAddressOwnership(addressId, userId, (err, owns) => {
        if (err) {
            return res.status(500).json({
                message: "Error checking address ownership",
                error: err
            });
        }

        if (!owns) {
            return res.status(403).json({
                message: "Access denied: You don't own this address"
            });
        }

        AddressModel.getAddressById(addressId, (err, result) => {
            if (err) {
                res.status(500).json({
                    message: "Error retrieving address",
                    error: err
                });
            } else if (!result) {
                res.status(404).json({
                    message: "Address not found"
                });
            } else {
                res.status(200).json(result);
            }
        });
    });
};

// Create new address
export const createAddress = (req, res) => {
    const userId = req.params.userId;
    const addressData = {
        user_id: userId,
        address_label: req.body.address_label || 'Home',
        recipient_name: req.body.recipient_name,
        recipient_phone: req.body.recipient_phone,
        address_line1: req.body.address_line1,
        address_line2: req.body.address_line2,
        district: req.body.district,
        city: req.body.city,
        postal_code: req.body.postal_code,
        country: req.body.country || 'Vietnam',
        is_default: req.body.is_default || 0
    };

    // Validate required fields
    if (!addressData.recipient_name || !addressData.recipient_phone ||
        !addressData.address_line1 || !addressData.city) {
        return res.status(400).json({
            message: "Missing required fields: recipient_name, recipient_phone, address_line1, city"
        });
    }

    AddressModel.createAddress(addressData, (err, result) => {
        if (err) {
            res.status(500).json({
                message: "Error creating address",
                error: err
            });
        } else {
            res.status(201).json({
                message: "Address created successfully",
                address: result
            });
        }
    });
};

// Update address
export const updateAddress = (req, res) => {
    const addressId = req.params.addressId;
    const userId = req.user?.user_id || req.params.userId;

    // First check if user owns this address
    AddressModel.checkAddressOwnership(addressId, userId, (err, owns) => {
        if (err) {
            return res.status(500).json({
                message: "Error checking address ownership",
                error: err
            });
        }

        if (!owns) {
            return res.status(403).json({
                message: "Access denied: You don't own this address"
            });
        }

        const addressData = {
            address_label: req.body.address_label,
            recipient_name: req.body.recipient_name,
            recipient_phone: req.body.recipient_phone,
            address_line1: req.body.address_line1,
            address_line2: req.body.address_line2,
            district: req.body.district,
            city: req.body.city,
            postal_code: req.body.postal_code,
            country: req.body.country,
            is_default: req.body.is_default || 0
        };

        AddressModel.updateAddress(addressId, addressData, (err, result) => {
            if (err) {
                res.status(500).json({
                    message: "Error updating address",
                    error: err
                });
            } else {
                res.status(200).json({
                    message: "Address updated successfully"
                });
            }
        });
    });
};

// Delete address
export const deleteAddress = (req, res) => {
    const addressId = req.params.addressId;
    const userId = req.user?.user_id || req.params.userId;

    // First check if user owns this address
    AddressModel.checkAddressOwnership(addressId, userId, (err, owns) => {
        if (err) {
            return res.status(500).json({
                message: "Error checking address ownership",
                error: err
            });
        }

        if (!owns) {
            return res.status(403).json({
                message: "Access denied: You don't own this address"
            });
        }

        AddressModel.deleteAddress(addressId, (err, result) => {
            if (err) {
                res.status(500).json({
                    message: "Error deleting address",
                    error: err
                });
            } else {
                res.status(200).json({
                    message: "Address deleted successfully"
                });
            }
        });
    });
};

// Set address as default
export const setDefaultAddress = (req, res) => {
    const addressId = req.params.addressId;
    const userId = req.params.userId;

    // First check if user owns this address
    AddressModel.checkAddressOwnership(addressId, userId, (err, owns) => {
        if (err) {
            return res.status(500).json({
                message: "Error checking address ownership",
                error: err
            });
        }

        if (!owns) {
            return res.status(403).json({
                message: "Access denied: You don't own this address"
            });
        }

        AddressModel.setDefaultAddress(addressId, userId, (err, result) => {
            if (err) {
                res.status(500).json({
                    message: "Error setting default address",
                    error: err
                });
            } else {
                res.status(200).json({
                    message: "Default address updated successfully"
                });
            }
        });
    });
};

// Get address count for user
export const getUserAddressCount = (req, res) => {
    const userId = req.params.userId;

    AddressModel.getUserAddressCount(userId, (err, count) => {
        if (err) {
            res.status(500).json({
                message: "Error getting address count",
                error: err
            });
        } else {
            res.status(200).json({
                count: count
            });
        }
    });
};
