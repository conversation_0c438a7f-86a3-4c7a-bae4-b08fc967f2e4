<template>
    <div class="md-login">
        <div class="md-login__container md-surface-container">
            <div class="md-login__header">
                <span class="material-icons-round md-login__icon">restaurant</span>
                <h2 class="md-login__title">Welcome Back</h2>
                <p class="md-login__subtitle">Sign in to continue to FoodDeli</p>
            </div>

            <form id="loginForm" @submit="handleSubmit" novalidate autocomplete="off" class="md-login__form">
                <div v-if="errors.length" class="md-alert md-alert--error">
                    <span class="material-icons-round md-alert__icon">error</span>
                    <div class="md-alert__content">
                        <ul class="md-alert__list">
                            <li v-for="error in errors" :key="error" class="md-alert__item">{{ error }}</li>
                        </ul>
                    </div>
                </div>

                <div class="md-textfield">
                    <label for="uEmail" class="md-textfield__label">Email</label>
                    <div class="md-textfield__container">
                        <span class="material-icons-round md-textfield__icon">email</span>
                        <input 
                            type="email" 
                            id="uEmail" 
                            name="uEmail" 
                            class="md-textfield__input" 
                            placeholder="Enter your email address"
                            v-model="loginObj.email" 
                        />
                    </div>
                </div>

                <div class="md-textfield">
                    <label for="uPass" class="md-textfield__label">Password</label>
                    <div class="md-textfield__container">
                        <span class="material-icons-round md-textfield__icon">lock</span>
                        <input 
                            type="password" 
                            id="uPass" 
                            name="uPass" 
                            class="md-textfield__input"
                            placeholder="Enter your password" 
                            v-model="loginObj.pass" 
                        />
                    </div>
                </div>

                <button type="submit" class="md-button md-button--filled md-login__button">
                    <span class="material-icons-round">login</span>
                    Sign In
                </button>
                
                <div class="md-login__footer">
                    <p class="md-login__text">Don't have an account? 
                        <router-link to="/register" class="md-login__link">Create One</router-link>
                    </p>
                </div>
            </form>
        </div>
    </div>
</template>

<script>
import apiService from "../services/ApiService";
import { mapMutations } from "vuex";
export default {
    name: 'Login',

    data() {
        return {
            loginObj: { email: "", pass: "" },
            matchUser: undefined,
            errors: [],
        }
    },

    methods: {
        ...mapMutations(["setUser"]),

        scrollToTop() {
            window.scrollTo(0, 0);
        },

        async getMatchUser(email) {
            try {
                this.matchUser = await apiService.getUserByEmail(email);
            } catch (error) {
                console.error('Error fetching user:', error);
                this.matchUser = null;
            }
        },

        async handleSubmit(e) {
            this.errors = [];

            if (!this.loginObj.email) {
                this.errors.push("Entering a email is required");
            }
            else {
                if (!/[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$/.test(this.loginObj.email)) {
                    this.errors.push('Email must be valid');
                }
            }


            if (!this.loginObj.pass) {
                this.errors.push('Password is required');
            }

            if (!this.errors.length == 0) {
                e.preventDefault();
            }
            else {
                e.preventDefault();
                try {
                    // Use the new login method from ApiService
                    const authenticatedUser = await apiService.loginUser(this.loginObj.email, this.loginObj.pass);
                    
                    // Set the user in the Vuex store and redirect
                    this.setUser(authenticatedUser);
                    this.$router.push("/");
                } catch (error) {
                    // Handle authentication failure
                    console.error('Login error:', error);
                    this.errors.push("Incorrect email or password!");
                }
            }
        }

    }

}
</script>

<style scoped>
.md-login {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: var(--md-sys-spacing-large);
    background: url("../assets/images/taco-background.png") no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;
}

.md-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.md-login__container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 450px;
    border-radius: var(--md-sys-shape-large);
    padding: var(--md-sys-spacing-large);
    box-shadow: var(--md-sys-elevation-3);
    background-color: var(--md-sys-color-surface);
}

.md-login__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: var(--md-sys-spacing-large);
    text-align: center;
}

.md-login__icon {
    font-size: 48px;
    color: var(--md-sys-color-primary);
    margin-bottom: var(--md-sys-spacing-small);
}

.md-login__title {
    color: var(--md-sys-color-on-surface);
    font-family: var(--md-sys-typescale-headline-medium-font-family-name);
    font-size: var(--md-sys-typescale-headline-medium-font-size);
    font-weight: var(--md-sys-typescale-headline-medium-font-weight);
    margin: 0 0 var(--md-sys-spacing-small);
}

.md-login__subtitle {
    color: var(--md-sys-color-on-surface-variant);
    font-family: var(--md-sys-typescale-body-medium-font-family-name);
    font-size: var(--md-sys-typescale-body-medium-font-size);
    margin: 0;
}

.md-login__form {
    display: flex;
    flex-direction: column;
    gap: var(--md-sys-spacing-medium);
}

.md-alert {
    display: flex;
    padding: var(--md-sys-spacing-medium);
    border-radius: var(--md-sys-shape-medium);
    align-items: flex-start;
    gap: var(--md-sys-spacing-small);
}

.md-alert--error {
    background-color: var(--md-sys-color-error-container);
    color: var(--md-sys-color-on-error-container);
}

.md-alert__icon {
    color: var(--md-sys-color-on-error-container);
}

.md-alert__list {
    margin: 0;
    padding-left: var(--md-sys-spacing-large);
}

.md-alert__item {
    margin-bottom: var(--md-sys-spacing-small);
}

.md-alert__item:last-child {
    margin-bottom: 0;
}

.md-textfield {
    display: flex;
    flex-direction: column;
    gap: var(--md-sys-spacing-small);
}

.md-textfield__label {
    color: var(--md-sys-color-on-surface-variant);
    font-family: var(--md-sys-typescale-body-medium-font-family-name);
    font-size: var(--md-sys-typescale-body-medium-font-size);
    font-weight: 500;
}

.md-textfield__container {
    display: flex;
    align-items: center;
    background-color: var(--md-sys-color-surface-container-highest);
    border-radius: var(--md-sys-shape-medium);
    border: 1px solid var(--md-sys-color-outline);
    padding: 0 var(--md-sys-spacing-medium);
    transition: border-color 0.2s ease;
}

.md-textfield__container:focus-within {
    border-color: var(--md-sys-color-primary);
}

.md-textfield__icon {
    color: var(--md-sys-color-on-surface-variant);
    margin-right: var(--md-sys-spacing-small);
}

.md-textfield__input {
    width: 100%;
    padding: var(--md-sys-spacing-medium) 0;
    border: none;
    background: transparent;
    color: var(--md-sys-color-on-surface);
    font-family: var(--md-sys-typescale-body-large-font-family-name);
    font-size: var(--md-sys-typescale-body-large-font-size);
    outline: none;
}

.md-textfield__input::placeholder {
    color: var(--md-sys-color-on-surface-variant);
    opacity: 0.7;
}

.md-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--md-sys-spacing-small);
    padding: var(--md-sys-spacing-medium) var(--md-sys-spacing-large);
    border-radius: var(--md-sys-shape-medium);
    font-family: var(--md-sys-typescale-label-large-font-family-name);
    font-size: var(--md-sys-typescale-label-large-font-size);
    font-weight: var(--md-sys-typescale-label-large-font-weight);
    text-transform: uppercase;
    letter-spacing: var(--md-sys-typescale-label-large-letter-spacing);
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.md-button--filled {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
}

.md-button--filled:hover {
    background-color: var(--md-sys-color-primary-hover);
}

.md-login__button {
    margin-top: var(--md-sys-spacing-small);
}

.md-login__footer {
    margin-top: var(--md-sys-spacing-medium);
    text-align: center;
}

.md-login__text {
    color: var(--md-sys-color-on-surface-variant);
    font-family: var(--md-sys-typescale-body-medium-font-family-name);
    font-size: var(--md-sys-typescale-body-medium-font-size);
    margin: 0;
}

.md-login__link {
    color: var(--md-sys-color-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.md-login__link:hover {
    color: var(--md-sys-color-primary-hover);
    text-decoration: underline;
}

/* Add responsive styles */
@media (max-width: 600px) {
    .md-login__container {
        padding: var(--md-sys-spacing-medium);
    }
    
    .md-login__title {
        font-size: var(--md-sys-typescale-headline-small-font-size);
    }
}

.login-container .login-form-container form .error-box ul {
    list-style-type: none;
    margin: 0;
    padding: 10px 0px;
}

.login-container .login-form-container form .error-box ul li {
    padding-left: 10px;
    color: rgb(182, 0, 0);
}
</style>