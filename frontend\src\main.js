import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Material Design Web Components
import '@material/web/button/filled-button.js';
import '@material/web/button/outlined-button.js';
import '@material/web/button/text-button.js';
import '@material/web/iconbutton/icon-button.js';
import '@material/web/textfield/filled-text-field.js';
import '@material/web/select/filled-select.js';
import '@material/web/select/select-option.js';
import '@material/web/icon/icon.js';

// Google Fonts & Icons
import '@fontsource/roboto';
import 'material-icons/iconfont/material-icons.css';
import 'material-icons-font/material-icons-font.css';

// Create the Vue app
const app = createApp(App);

app.use(store)
app.use(router)

// Initialize API data fetching
console.log('Using real data from backend API')
store.commit('setUseMockData', false)
store.dispatch('getFoodsData')

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info);
}

app.mount('#app')