<template>
  <header class="md-app-bar">
    <div class="md-app-bar__container">
      <!-- Logo Section -->
      <div class="md-app-bar__section">
        <router-link to="/" class="md-app-bar__logo">
          <img src="/taco.png" alt="FoodDeli Logo" class="md-app-bar__logo-image">
          <span class="md-app-bar__title">FoodDeli</span>
        </router-link>
      </div>

      <!-- Search Section -->
      <div class="md-app-bar__section md-app-bar__section--search">
        <div class="md-search-field">
          <span class="material-icons-round">search</span>
          <input
            type="search"
            placeholder="Search for dishes..."
            class="md-search-field__input"
          >
          <button class="md-search-field__voice">
            <span class="material-icons-round">mic</span>
          </button>
        </div>
      </div>

      <!-- Navigation Section (Desktop Only) -->
      <nav class="md-app-bar__section md-app-bar__section--nav">
        <router-link
          v-for="item in displayNavItems"
          :key="item.path"
          :to="item.path"
          class="md-nav-link"
          active-class="md-nav-link--active"
        >
          <span class="material-icons-round">{{ item.icon }}</span>
          <span>{{ item.label }}</span>
        </router-link>

        <!-- Auth Buttons -->
        <div class="md-app-bar__auth" v-if="!user">
          <router-link to="/login" class="md-button md-button--surface">Login</router-link>
          <router-link to="/register" class="md-button md-button--primary">Register</router-link>
        </div>
        <div class="md-app-bar__auth" v-else>
          <router-link to="/my-reviews" class="md-button md-button--surface">
            <span class="material-icons-round">rate_review</span>
            Reviews
          </router-link>
          <router-link to="/address-management" class="md-button md-button--surface">
            <span class="material-icons-round">location_on</span>
            Addresses
          </router-link>
          <router-link to="/profile" class="md-button md-button--surface">Profile</router-link>
          <router-link to="/cart" class="md-button md-button--surface">Cart</router-link>
          <button @click="logout" class="md-button md-button--surface">Logout</button>
        </div>
      </nav>

      <!-- Mobile Menu Button -->
      <button class="md-app-bar__menu md-icon-button">
        <span class="material-icons-round">menu</span>
      </button>
    </div>
  </header>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'AppBar',
  data() {
    return {
      navItems: [
        { path: '/', label: 'Home', icon: 'home' },
        { path: '/menu', label: 'Menu', icon: 'restaurant_menu' },
        { path: '/promotions', label: 'Promos', icon: 'local_offer' },
        { path: '/about', label: 'About', icon: 'info' }
      ]
    }
  },
  computed: {
    ...mapState(['user']),
    displayNavItems() {
      // Base navigation items for all users
      const items = [...this.navItems];

      // Add admin link if user is an admin
      if (this.user && this.user.role === 'admin') {
        items.push({ path: '/admin', label: 'Admin', icon: 'admin_panel_settings' });
      }

      return items;
    }
  },
  methods: {
    logout() {
      this.$store.commit('setUser', null);
      this.$router.push('/');
    }
  }
}
</script>

<style scoped>
.md-app-bar {
  position: sticky;
  top: 0;
  background-color: var(--md-sys-color-surface);
  box-shadow: var(--md-elevation-level2);
  z-index: 1000;
  padding: var(--md-sys-spacing-small);
}

.md-app-bar__container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-medium);
}

.md-app-bar__section {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-medium);
}

.md-app-bar__section--search {
  flex: 1;
}

.md-app-bar__logo {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  text-decoration: none;
  color: var(--md-sys-color-on-surface);
}

.md-app-bar__logo-image {
  height: 32px;
  width: auto;
}

.md-app-bar__title {
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: 500;
}

.md-search-field {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  background-color: var(--md-sys-color-surface-variant);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border-radius: var(--md-sys-shape-corner-full);
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.md-search-field__input {
  flex: 1;
  border: none;
  background: none;
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
  outline: none;
}

.md-search-field__voice {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  padding: var(--md-sys-spacing-extra-small);
  border-radius: var(--md-sys-shape-corner-full);
  transition: background-color var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
}

.md-search-field__voice:hover {
  background-color: var(--md-sys-color-surface);
}

.md-app-bar__section--nav {
  display: none;
}

.md-nav-link {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  border-radius: var(--md-sys-shape-corner-full);
  transition: all var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
}

.md-nav-link:hover {
  background-color: var(--md-sys-color-surface-variant);
}

.md-nav-link--active {
  color: var(--md-sys-color-primary);
}

.md-app-bar__auth {
  display: flex;
  gap: var(--md-sys-spacing-small);
  margin-left: var(--md-sys-spacing-medium);
}

.md-icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--md-sys-color-on-surface);
  cursor: pointer;
  border-radius: var(--md-sys-shape-corner-full);
  transition: background-color var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
}

.md-icon-button:hover {
  background-color: var(--md-sys-color-surface-variant);
}

/* Desktop styles */
@media (min-width: 1024px) {
  .md-app-bar {
    padding: var(--md-sys-spacing-medium);
  }

  .md-app-bar__section--nav {
    display: flex;
  }

  .md-app-bar__menu {
    display: none;
  }
}</style>