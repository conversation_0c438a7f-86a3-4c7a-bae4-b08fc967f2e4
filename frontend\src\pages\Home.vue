<template>
  <div class="md-home">
    <!-- Hero Section with modern curved design -->
    <section class="md-hero md-surface-container">
      <div class="md-hero__content">
        <h1 class="md-hero__title">
          <span class="md-hero__subtitle">Welcome to FoodDeli 🌮</span>
          Authentic Mexican Flavors
        </h1>
        <p class="md-hero__text">Experience explosive flavors crafted by our 5-star Michelin chef. Fresh ingredients, traditional recipes, modern twist.</p>
        <div class="md-hero__actions">
          <button class="md-button md-button--primary" @click="$router.push('/menu')">
            <span class="material-icons-round">restaurant_menu</span>
            Order Now
          </button>
          <button class="md-button md-button--outline" @click="$router.push('/about')">
            <span class="material-icons-round">info</span>
            Learn More
          </button>
        </div>
      </div>
      <div class="md-hero__media">
        <img src="../assets/images/b.png" alt="Featured dishes" class="md-hero__image">
        <div class="md-hero__floating-chips">
          <span class="md-chip">
            <span class="material-icons-round">eco</span>
            Fresh Daily
          </span>
          <span class="md-chip">
            <span class="material-icons-round">grade</span>
            Best Quality
          </span>
          <span class="md-chip">
            <span class="material-icons-round">delivery_dining</span>
            Fast Delivery
          </span>
        </div>
      </div>
    </section>

    <CategoryChips @category-selected="onCategorySelected" />

    <!-- Category Scroll Section -->
    <section class="md-categories">
      <div class="md-section-header">
        <h2 class="md-section-header__title">Popular Categories</h2>
        <p class="md-section-header__subtitle">Explore our delicious options</p>
      </div>
      <div class="md-categories__scroll">
        <div v-for="category in categories" :key="category.id"
             class="md-card md-category-card" @click="$router.push(`/menu?category=${category.name.toLowerCase()}`)">
          <div class="md-card__media md-category-card__media">
            <img :src="require(`../assets/images/${category.image}`)" :alt="category.name">
          </div>
          <div class="md-card__content">
            <h3 class="md-card__title">{{ category.name }}</h3>
            <p class="md-card__subtitle">{{ category.itemCount }} items</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Dynamic Promotions Carousel -->
    <section class="md-promotions">
      <div class="md-section-header">
        <h2 class="md-section-header__title">Special Offers</h2>
        <p class="md-section-header__subtitle">Limited time deals</p>
      </div>
      <div class="md-carousel">
        <div class="md-carousel__container"
             :style="{ transform: `translateX(-${currentPromo * 100}%)` }">
          <div v-for="promo in promotions" :key="promo.id" class="md-carousel__slide">
            <div class="md-card md-promo-card">
              <div class="md-card__media">
                <img :src="require(`../assets/images/${promo.image}`)" :alt="promo.title">
              </div>
              <div class="md-promo-card__content">
                <span class="md-chip">
                  <span class="material-icons-round">local_offer</span>
                  {{ promo.tag }}
                </span>
                <h3 class="md-card__title">{{ promo.title }}</h3>
                <p class="md-card__subtitle">{{ promo.description }}</p>
                <button class="md-button md-button--primary" @click="$router.push(promo.link)">
                  <span class="material-icons-round">shopping_cart</span>
                  Order Now
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="md-carousel__controls">
          <button v-for="(_, index) in promotions"
                  :key="index"
                  :class="['md-carousel__dot', { 'md-carousel__dot--active': currentPromo === index }]"
                  @click="currentPromo = index">
          </button>
        </div>
      </div>
    </section>

    <!-- Features Grid -->
    <section class="md-features">
      <div class="md-section-header">
        <h2 class="md-section-header__title">Why Choose Us</h2>
        <p class="md-section-header__subtitle">What makes us special</p>
      </div>
      <div class="md-features__grid">
        <div v-for="feature in features" :key="feature.id" class="md-card md-feature-card">
          <span class="material-icons-round md-feature-card__icon">{{ feature.icon }}</span>
          <h3 class="md-card__title">{{ feature.title }}</h3>
          <p class="md-card__subtitle">{{ feature.description }}</p>
        </div>
      </div>
    </section>

    <!-- Floating Action Button -->
    <button class="md-fab" @click="$router.push('/menu')">
      <span class="material-icons-round">restaurant_menu</span>
    </button>
  </div>
</template>

<script>
import CategoryChips from '@/components/CategoryChips.vue';
export default {
  name: "Home",
  components: { CategoryChips },
  data() {
    return {
      currentPromo: 0,
      categories: [
        { id: 1, name: 'Tacos', image: 'taco-img.png', itemCount: 10 },
        { id: 2, name: 'Burritos', image: 'burrito-img.png', itemCount: 8 },
        { id: 3, name: 'Nachos', image: 'nachos-img.png', itemCount: 12 },
        { id: 4, name: 'Sides', image: 'salad-img.png', itemCount: 6 },
        { id: 5, name: 'Desserts', image: 'dessert-img.png', itemCount: 5 },
        { id: 6, name: 'Drinks', image: 'coca-img.png', itemCount: 7 },
      ],
      promotions: [
        {
          id: 1,
          image: 'dis-1.jpg',
          tag: 'special offer',
          title: 'Up to 50% off',
          description: 'Enjoy up to 50% off on selected items.',
          link: '/menu'
        },
        {
          id: 2,
          image: 'dis-2.png',
          tag: 'new arrival',
          title: 'Try Our New Tacos',
          description: 'Taste the new flavors of our tacos.',
          link: '/menu?category=tacos'
        },
        {
          id: 3,
          image: 'dis-3.jpg',
          tag: 'limited time',
          title: 'Free Drink with $20+',
          description: 'Get a free drink with orders over $20.',
          link: '/menu?category=drinks'
        }
      ],
      features: [
        { id: 1, title: 'Fast Delivery', icon: 'delivery_dining', description: 'Get your food delivered quickly and hot.' },
        { id: 2, title: 'Fresh Food', icon: 'eco', description: 'We use only the freshest ingredients.' },
        { id: 3, title: 'Best Quality', icon: 'grade', description: 'Our dishes are crafted with care and precision.' },
        { id: 4, title: '24/7 Support', icon: 'support_agent', description: 'We are here to assist you anytime.' },
      ]
    }
  },
  mounted() {
    // Auto-advance carousel
    setInterval(() => {
      this.currentPromo = (this.currentPromo + 1) % this.promotions.length;
    }, 5000);
  },
  methods: {
    onCategorySelected(id) {
      this.$router.push(`/menu?category=${id}`);
    }
  }
};
</script>

<style scoped>
.md-home {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-large);
  padding-bottom: 80px; /* Space for bottom navigation */
}

.md-hero {
  position: relative;
  padding: var(--md-sys-spacing-large);
  border-radius: var(--md-sys-shape-corner-large);
  background: linear-gradient(135deg, var(--md-sys-color-primary) 0%, var(--md-sys-color-secondary) 100%);
  overflow: hidden;
  margin: var(--md-sys-spacing-medium);
  box-shadow: var(--md-elevation-level2);
}

.md-hero__content {
  position: relative;
  z-index: 1;
  max-width: 600px;
}

.md-hero__title {
  font-family: var(--md-sys-typescale-display-large-font);
  font-size: var(--md-sys-typescale-display-large-size);
  color: var(--md-sys-color-on-primary);
  margin-bottom: var(--md-sys-spacing-medium);
  line-height: 1.1;
}

.md-hero__subtitle {
  display: block;
  color: var(--md-sys-color-on-primary);
  font-size: var(--md-sys-typescale-headline-small-size);
  margin-bottom: var(--md-sys-spacing-small);
  opacity: 0.9;
}

.md-hero__text {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-primary);
  margin-bottom: var(--md-sys-spacing-large);
  opacity: 0.9;
  line-height: 1.5;
}

.md-hero__actions {
  display: flex;
  gap: var(--md-sys-spacing-medium);
  flex-wrap: wrap;
}

.md-hero__media {
  position: relative;
  margin-top: var(--md-sys-spacing-large);
}

.md-hero__image {
  width: 100%;
  height: auto;
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-elevation-level3);
}

.md-hero__floating-chips {
  position: absolute;
  display: flex;
  gap: var(--md-sys-spacing-small);
  flex-wrap: wrap;
  bottom: var(--md-sys-spacing-large);
  left: var(--md-sys-spacing-large);
}

/* Categories Section */
.md-categories {
  padding: var(--md-sys-spacing-medium);
}

.md-categories__scroll {
  display: flex;
  gap: var(--md-sys-spacing-medium);
  overflow-x: auto;
  padding: var(--md-sys-spacing-small) 0;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  padding-bottom: var(--md-sys-spacing-medium);
}

.md-categories__scroll::-webkit-scrollbar {
  display: none;
}

.md-category-card {
  flex: 0 0 auto;
  width: 160px;
  border-radius: var(--md-sys-shape-corner-large);
  background-color: var(--md-sys-color-surface);
  box-shadow: var(--md-elevation-level1);
  scroll-snap-align: start;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  overflow: hidden;
  cursor: pointer;
}

.md-category-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--md-elevation-level2);
}

.md-category-card__media {
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.md-category-card__media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--md-sys-motion-duration-medium1) var(--md-sys-motion-easing-standard);
}

.md-category-card:hover .md-category-card__media img {
  transform: scale(1.05);
}

/* Promotions Carousel */
.md-promotions {
  padding: 0 var(--md-sys-spacing-medium);
}

.md-carousel {
  position: relative;
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  box-shadow: var(--md-elevation-level2);
  margin-bottom: var(--md-sys-spacing-large);
}

.md-carousel__container {
  display: flex;
  transition: transform var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.md-carousel__slide {
  flex: 0 0 100%;
}

.md-promo-card {
  position: relative;
  height: auto;
  border-radius: 0;
  box-shadow: none;
}

.md-promo-card .md-card__media {
  height: 200px;
}

.md-promo-card .md-card__media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.md-promo-card__content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--md-sys-spacing-large);
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white !important;
}

/* Ensure all text elements in promo card are white */
.md-promo-card__content * {
  color: white !important;
}

.md-promo-card__content .md-chip {
  background-color: rgba(255, 255, 255, 0.2);
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.md-promo-card__content .md-chip .material-icons-round {
  color: white !important;
}

.md-promo-card__content .md-card__title {
  color: white !important;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.md-promo-card__content .md-card__subtitle {
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.md-promo-card__content .md-button {
  background-color: var(--md-sys-color-primary);
  color: white !important;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.md-promo-card__content .md-button .material-icons-round {
  color: white !important;
}

.md-carousel__controls {
  position: absolute;
  bottom: var(--md-sys-spacing-medium);
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: var(--md-sys-spacing-small);
}

.md-carousel__dot {
  width: 8px;
  height: 8px;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-carousel__dot--active {
  background-color: var(--md-sys-color-primary);
  width: 24px;
}

/* Features Grid */
.md-features {
  padding: var(--md-sys-spacing-large) var(--md-sys-spacing-medium);
}

.md-features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--md-sys-spacing-medium);
}

.md-feature-card {
  padding: var(--md-sys-spacing-large);
  text-align: center;
  cursor: pointer;
}

.md-feature-card__icon {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-medium);
  display: block;
}

/* Desktop Styles */
@media (min-width: 768px) {
  .md-home {
    padding-bottom: 0; /* No bottom navigation on desktop */
  }

  .md-hero {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-extra-large);
    min-height: 500px;
    padding: var(--md-sys-spacing-extra-large);
    margin: var(--md-sys-spacing-large);
  }

  .md-hero__content {
    flex: 1;
  }

  .md-hero__media {
    flex: 1;
    margin-top: 0;
  }

  .md-categories__scroll {
    overflow-x: visible;
    flex-wrap: wrap;
    justify-content: center;
  }

  .md-category-card {
    width: 200px;
  }

  .md-category-card__media {
    height: 140px;
  }

  .md-features__grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--md-sys-spacing-large);
  }

  .md-promo-card .md-card__media {
    height: 300px;
  }

  .md-fab {
    bottom: var(--md-sys-spacing-large);
  }
}</style>
