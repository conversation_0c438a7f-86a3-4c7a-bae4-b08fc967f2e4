import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import db from '../config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to run SQL file
const runSQLFile = (filePath) => {
  return new Promise((resolve, reject) => {
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Split by semicolon and filter out empty statements
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    let completed = 0;
    const total = statements.length;
    
    if (total === 0) {
      resolve();
      return;
    }
    
    statements.forEach((statement, index) => {
      db.query(statement.trim(), (err, result) => {
        if (err) {
          console.error(`Error executing statement ${index + 1}:`, err);
          console.error('Statement:', statement.trim());
          reject(err);
          return;
        }
        
        completed++;
        console.log(`✓ Executed statement ${completed}/${total}`);
        
        if (completed === total) {
          resolve();
        }
      });
    });
  });
};

// Main migration function
const runMigrations = async () => {
  try {
    console.log('🚀 Starting database migrations...');
    
    const migrationsDir = path.join(__dirname, '../migrations');
    
    // Check if migrations directory exists
    if (!fs.existsSync(migrationsDir)) {
      console.log('❌ Migrations directory not found');
      return;
    }
    
    // Get all SQL files in migrations directory
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Run in alphabetical order
    
    if (migrationFiles.length === 0) {
      console.log('📝 No migration files found');
      return;
    }
    
    console.log(`📁 Found ${migrationFiles.length} migration files`);
    
    // Run each migration file
    for (const file of migrationFiles) {
      const filePath = path.join(migrationsDir, file);
      console.log(`\n📄 Running migration: ${file}`);
      
      try {
        await runSQLFile(filePath);
        console.log(`✅ Migration ${file} completed successfully`);
      } catch (error) {
        console.error(`❌ Migration ${file} failed:`, error);
        throw error;
      }
    }
    
    console.log('\n🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    db.end();
  }
};

// Run migrations if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations();
}

export default runMigrations;
