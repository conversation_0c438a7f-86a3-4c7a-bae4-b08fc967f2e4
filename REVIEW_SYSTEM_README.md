# 🎯 Hệ Thống Theo <PERSON> Giá và Phản Hồi Khách Hàng

## 📋 Tổng Quan

Hệ thống đánh giá và phản hồi khách hàng được thiết kế để cho phép khách hàng đánh giá món ăn sau khi hoàn tất đơn hàng, đồng thời cung cấp cho quản trị viên và chủ nhà hàng các công cụ để theo dõi, phản hồi và phân tích đánh giá nhằm cải thiện chất lượng dịch vụ.

## 🚀 Tính Năng Chính

### 👥 Dành cho Khách Hàng
- ⭐ Đánh giá món ăn với hệ thống 5 sao
- 💬 Viết bình luận chi tiết về trải nghiệm
- ✏️ Chỉnh sửa và xóa đánh giá của mình
- 📱 Xem tất cả đánh giá đã viết trong trang "My Reviews"
- 🚩 <PERSON>áo cáo đánh giá không phù hợp
- 👀 Xem phản hồi từ nhà hàng

### 🔧 Dành cho Admin/Quản Trị Viên
- 📊 Dashboard thống kê đánh giá tổng quan
- 📝 Quản lý tất cả đánh giá với phân trang và lọc
- 💬 Phản hồi trực tiếp đến đánh giá của khách hàng
- 🚩 Xử lý các báo cáo đánh giá
- 👁️ Ẩn hoặc xóa đánh giá không phù hợp
- 📈 Phân tích xu hướng đánh giá theo thời gian

## 🗄️ Cấu Trúc Database

### Bảng `reviews`
```sql
CREATE TABLE `reviews` (
  `review_id` int NOT NULL AUTO_INCREMENT,
  `food_id` int NOT NULL,
  `user_id` int NOT NULL,
  `rating` int NOT NULL CHECK (rating >= 1 AND rating <= 5),
  `comment` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `reported` tinyint(1) DEFAULT '0',
  `report_reason` text,
  `hidden` tinyint(1) DEFAULT '0',
  `admin_notes` text,
  PRIMARY KEY (`review_id`),
  UNIQUE KEY `unique_user_food_review` (`user_id`,`food_id`)
);
```

### Bảng `review_responses`
```sql
CREATE TABLE `review_responses` (
  `response_id` INT AUTO_INCREMENT PRIMARY KEY,
  `review_id` INT NOT NULL,
  `response` TEXT NOT NULL,
  `admin_id` INT,
  `response_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`review_id`) REFERENCES `reviews`(`review_id`) ON DELETE CASCADE
);
```

## 🛠️ Cài Đặt và Triển Khai

### 1. Chạy Database Migrations
```bash
cd backend
node scripts/run-migrations.js
```

### 2. Cập nhật Backend Dependencies
Đảm bảo các dependencies sau đã được cài đặt:
```bash
npm install express mysql2 cors
```

### 3. Cập nhật Frontend Dependencies
```bash
cd frontend
npm install axios vue-router@4 vuex@4
```

### 4. Khởi động Server
```bash
# Backend
cd backend
npm start

# Frontend
cd frontend
npm run serve
```

## 📡 API Endpoints

### Customer APIs
- `GET /api/foods/:foodId/reviews` - Lấy tất cả đánh giá của món ăn
- `GET /api/users/:userId/foods/:foodId/review` - Lấy đánh giá của user cho món ăn
- `POST /api/reviews` - Tạo đánh giá mới
- `PUT /api/reviews/:reviewId` - Cập nhật đánh giá
- `DELETE /api/reviews/:reviewId` - Xóa đánh giá
- `POST /api/reviews/:reviewId/report` - Báo cáo đánh giá
- `GET /api/users/:userId/reviews` - Lấy tất cả đánh giá của user

### Admin APIs
- `GET /api/admin/reviews/stats` - Thống kê đánh giá
- `GET /api/admin/reviews` - Lấy tất cả đánh giá (có phân trang)
- `POST /api/admin/reviews/:reviewId/response` - Phản hồi đánh giá
- `PUT /api/admin/reviews/:reviewId/status` - Cập nhật trạng thái đánh giá

## 🎨 Components và Pages

### Frontend Components
- `ReviewsSection.vue` - Component hiển thị đánh giá trong trang chi tiết món ăn
- `MyReviews.vue` - Trang quản lý đánh giá cá nhân
- `ReviewManagement.vue` - Trang quản lý đánh giá cho admin

### Routes
- `/my-reviews` - Trang đánh giá cá nhân
- `/admin/review-management` - Trang quản lý đánh giá admin

## 🔄 Workflow Sử Dụng

### Khách Hàng
1. Đặt hàng và hoàn tất thanh toán
2. Truy cập trang chi tiết món ăn
3. Viết đánh giá và chấm điểm
4. Xem phản hồi từ nhà hàng (nếu có)
5. Quản lý đánh giá trong trang "My Reviews"

### Admin
1. Truy cập Admin Dashboard
2. Xem thống kê đánh giá tổng quan
3. Vào Review Management để quản lý chi tiết
4. Phản hồi đánh giá của khách hàng
5. Xử lý các báo cáo và đánh giá không phù hợp

## 📊 Tính Năng Thống Kê

### Metrics Được Theo Dõi
- Tổng số đánh giá
- Điểm đánh giá trung bình
- Phân bố đánh giá theo sao (1-5)
- Số đánh giá bị báo cáo
- Đánh giá theo thời gian (hôm nay, tuần, tháng)

### Auto-Update Rating
Hệ thống tự động cập nhật điểm trung bình và số lượt đánh giá của món ăn thông qua database triggers.

## 🔒 Bảo Mật và Validation

### Backend Validation
- Kiểm tra rating từ 1-5
- Xác thực user đã đăng nhập
- Ngăn chặn đánh giá trùng lặp (1 user/1 món ăn)
- Sanitize input để tránh XSS

### Frontend Validation
- Kiểm tra đăng nhập trước khi cho phép đánh giá
- Validation form đánh giá
- Hiển thị thông báo lỗi rõ ràng

## 🎯 Tương Lai và Mở Rộng

### Tính Năng Có Thể Thêm
- 📸 Upload hình ảnh trong đánh giá
- 👍 Like/Dislike đánh giá
- 🏷️ Tag và phân loại đánh giá
- 📧 Email thông báo khi có đánh giá mới
- 📱 Push notification
- 🤖 AI sentiment analysis
- 📈 Advanced analytics và reporting

### Performance Optimization
- Caching đánh giá phổ biến
- Pagination cho danh sách đánh giá dài
- Image optimization cho đánh giá có hình
- Database indexing cho queries phức tạp

## 🐛 Troubleshooting

### Lỗi Thường Gặp
1. **Database connection error**: Kiểm tra config database
2. **Migration failed**: Đảm bảo quyền truy cập database
3. **API 404**: Kiểm tra routes đã được import đúng
4. **Frontend không hiển thị**: Kiểm tra API endpoints và CORS

### Debug Tips
- Kiểm tra console logs trong browser
- Xem network tab để debug API calls
- Kiểm tra database logs
- Sử dụng Postman để test APIs

## 📞 Hỗ Trợ

Nếu gặp vấn đề trong quá trình triển khai hoặc sử dụng, vui lòng:
1. Kiểm tra logs chi tiết
2. Xem lại documentation
3. Tạo issue với thông tin chi tiết về lỗi

---

**Phiên bản**: 1.0.0  
**Ngày cập nhật**: 2024  
**Tác giả**: Restaurant Ordering System Team
