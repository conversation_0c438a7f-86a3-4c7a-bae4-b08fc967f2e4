// import connection
import db from "../config/database.js";

// insert Bill Details
export const insertBillDetails = (data,result) => {
    db.query("INSERT INTO billdetails SET ?",data, (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results[0]);
        }
    });
};


// get Bill Details
export const getBillDetails = (id,result) => {
    const query = `
        SELECT bd.*, f.food_name as food_title, f.food_price as price, f.food_src as food_image, f.food_desc as description
        FROM billdetails bd
        JOIN food f ON bd.food_id = f.food_id
        WHERE bd.bill_id = ?
    `;

    db.query(query, [id], (err,results)=> {
        if (err){
            console.log(err);
            result(err,null);
        }else{
            result(null,results);
        }
    });
};