import { createWebHistory, createRouter } from "vue-router";
import Login from '../pages/Login.vue';
import Register from '../pages/Register.vue';
import Home from '../pages/Home.vue';
import About from '../pages/About.vue';
import Promo from '../pages/Promo.vue';
import Menu from '../pages/Menu.vue';
import Table from '../pages/Table.vue';
import Cart from '../pages/Cart.vue';
import Checkout from '../pages/Checkout.vue';
import Thank from '../pages/Thank.vue';
import MyOrder from '../pages/MyOrder.vue';
import Profile from '../pages/Profile.vue';
import FoodDetails from '../pages/FoodDetails.vue';
import MyReviews from '../pages/MyReviews.vue';
import AddressManagement from '../pages/AddressManagement.vue';
import Admin from '../admin/Admin.vue';
import Dashboard from '../admin/Dashboard.vue';
import UserManagement from '../admin/UserManagement.vue';
import MenuManagement from '../admin/MenuManagement.vue';
import OrderManagement from '../admin/OrderManagement.vue';
import ReviewManagement from '../admin/ReviewManagement.vue';

const routes = [
  {
    path: "/",
    name: "Home",
    component: Home,
  },
  {
    path: "/about",
    name: "About",
    component: About,
  },
  {
    path: "/promotions",
    name: "Promotions",
    component: Promo,
  },
  {
    path: "/menu",
    name: "Menu",
    component: Menu,
  },
  {
    path: "/table",
    name: "Table",
    component: Table,
  },
  {
    path: "/cart",
    name: "Cart",
    component: Cart,
  },
  {
    path: "/login",
    name: "Login",
    component: Login,
  },
  {
    path: "/register",
    name: "Register",
    component: Register,
  },
  {
    path: "/checkout",
    name: "Checkout",
    component: Checkout,
  },
  {
    path: "/thank",
    name: "Thank",
    component: Thank,
  },
  {
    path: "/myorder",
    name: "MyOrder",
    component: MyOrder,
  },
  {
    path: "/profile",
    name: "Profile",
    component: Profile,
  },
  {
    path: "/food/:id",
    name: "FoodDetails",
    component: FoodDetails,
    props: true,
  },
  {
    path: "/my-reviews",
    name: "MyReviews",
    component: MyReviews,
  },
  {
    path: "/address-management",
    name: "AddressManagement",
    component: AddressManagement,
  },
  {
    path: "/admin",
    name: "Admin",
    component: Admin,
  },
  {
    path: "/admin/dashboard",
    name: "Dashboard",
    component: Dashboard,
  },
  {
    path: "/admin/user-management",
    name: "UserManagement",
    component: UserManagement,
  },
  {
    path: "/admin/menu-management",
    name: "MenuManagement",
    component: MenuManagement,
  },
  {
    path: "/admin/order-management",
    name: "OrderManagement",
    component: OrderManagement,
  },
  {
    path: "/admin/review-management",
    name: "ReviewManagement",
    component: ReviewManagement,
  },
  {
    path: '/:pathMatch(.*)*',
    component: Home,
    // https://stackoverflow.com/questions/68504803/how-can-i-make-a-catch-all-other-route-in-vue-router-also-catch-the-url-when-p
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;