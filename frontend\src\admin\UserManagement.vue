<template>
    <div class="md-admin-dashboard">
        <div class="md-admin-header">
            <div class="md-admin-header__title-group">
                <h1 class="md-admin-header__title">
                    <i class="material-icons md-admin-header__icon">people</i>
                    User Management
                </h1>
                <p class="md-admin-header__subtitle">Manage user accounts</p>
            </div>
            
            <div class="md-admin-header__actions">
                <button class="md-button md-button--primary" @click="openCreateUserModal">
                    <i class="material-icons">person_add</i>
                    <span>Add User</span>
                </button>
                
                <button class="md-button md-button--text" @click="refreshUsers">
                    <i class="material-icons">refresh</i>
                    <span>Refresh</span>
                </button>
                
                <button class="md-button md-button--outlined md-button--warn" @click="navigateToDashboard">
                    <i class="material-icons">dashboard</i>
                    <span>Dashboard</span>
                </button>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div v-if="isLoading" class="md-loading-container">
            <div class="md-loading-indicator">
                <i class="material-icons spin">sync</i>
                <p>Loading users...</p>
            </div>
        </div>

        <!-- User Management Table -->
        <div v-else class="md-card md-admin-users">
            <div class="md-admin-users__header">
                <h2 class="md-admin-users__title">
                    <i class="material-icons">list</i>
                    User List
                </h2>
            </div>
            
            <div class="md-admin-users__content">
                <div class="md-admin-table-container">
                    <table class="md-admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="user in users" :key="user.user_id">
                                <td><span class="md-user-id">#{{ user.user_id }}</span></td>
                                <td>{{ user.user_name }}</td>
                                <td>{{ user.user_email }}</td>
                                <td>{{ user.user_phone || 'N/A' }}</td>
                                <td>
                                    <span :class="getRoleClass(user.role)">
                                        {{ user.role || 'user' }}
                                    </span>
                                </td>
                                <td>
                                    <span :class="getStatusClass(user.is_active)">
                                        {{ user.is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="md-admin-table__actions">
                                    <button class="md-button md-button--icon" @click="editUser(user)" title="Edit User">
                                        <i class="material-icons">edit</i>
                                    </button>
                                    <button 
                                        class="md-button md-button--icon" 
                                        :class="user.is_active ? 'md-button--warn' : 'md-button--success'"
                                        @click="toggleUserStatus(user)" 
                                        :title="user.is_active ? 'Deactivate User' : 'Activate User'">
                                        <i class="material-icons">{{ user.is_active ? 'lock' : 'lock_open' }}</i>
                                    </button>
                                    <button v-if="!isAdmin(user)" class="md-button md-button--icon md-button--danger" @click="confirmDeleteUser(user)" title="Delete User">
                                        <i class="material-icons">delete</i>
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="users.length === 0">
                                <td colspan="7" class="md-admin-table__empty">
                                    <i class="material-icons">people</i>
                                    <p>No users found</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Create/Edit User Modal -->
        <div v-if="showUserModal" class="md-modal">
            <div class="md-modal-backdrop" @click="closeUserModal"></div>
            <div class="md-modal-container">
                <div class="md-modal-header">
                    <h2 class="md-modal-title">
                        <i class="material-icons">{{ editMode ? 'edit' : 'person_add' }}</i>
                        {{ editMode ? 'Edit User' : 'Create New User' }}
                    </h2>
                    <button class="md-button md-button--icon" @click="closeUserModal">
                        <i class="material-icons">close</i>
                    </button>
                </div>

                <div v-if="formErrors.length" class="md-form-error-container">
                    <div class="md-form-error-icon">
                        <i class="material-icons">error</i>
                    </div>
                    <ul class="md-form-error-list">
                        <li v-for="error in formErrors" :key="error" class="md-form-error-item">
                            {{ error }}
                        </li>
                    </ul>
                </div>

                <div class="md-modal-content">
                    <form @submit.prevent="saveUser" class="md-user-form">
                        <div class="md-form-field">
                            <label for="userName" class="md-form-field__label">Full Name</label>
                            <div class="md-form-field__input-container">
                                <i class="material-icons md-form-field__icon">person</i>
                                <input 
                                    type="text" 
                                    id="userName" 
                                    class="md-form-field__input"
                                    v-model="userForm.name"
                                    placeholder="Enter user's full name"
                                    required />
                            </div>
                        </div>

                        <div class="md-form-field">
                            <label for="userEmail" class="md-form-field__label">Email</label>
                            <div class="md-form-field__input-container">
                                <i class="material-icons md-form-field__icon">email</i>
                                <input 
                                    type="email" 
                                    id="userEmail" 
                                    class="md-form-field__input"
                                    v-model="userForm.email"
                                    placeholder="Enter user's email address"
                                    required />
                            </div>
                        </div>

                        <div class="md-form-field">
                            <label for="userPhone" class="md-form-field__label">Phone (optional)</label>
                            <div class="md-form-field__input-container">
                                <i class="material-icons md-form-field__icon">phone</i>
                                <input 
                                    type="tel" 
                                    id="userPhone" 
                                    class="md-form-field__input"
                                    v-model="userForm.phone" 
                                    placeholder="Enter user's phone number" />
                            </div>
                        </div>

                        <div v-if="!editMode || (editMode && canChangePassword)" class="md-form-field">
                            <label for="userPassword" class="md-form-field__label">
                                {{ editMode ? 'New Password (leave blank to keep current)' : 'Password' }}
                            </label>
                            <div class="md-form-field__input-container">
                                <i class="material-icons md-form-field__icon">lock</i>
                                <input 
                                    type="password" 
                                    id="userPassword" 
                                    class="md-form-field__input"
                                    v-model="userForm.password"
                                    :placeholder="editMode ? 'Leave blank to keep current password' : 'Enter password'"
                                    :required="!editMode" />
                            </div>
                        </div>

                        <div class="md-form-field">
                            <label for="userRole" class="md-form-field__label">Role</label>
                            <div class="md-form-field__role-options">
                                <div 
                                    class="md-role-option" 
                                    :class="{'md-role-option--selected': userForm.role === 'user'}"
                                    @click="userForm.role = 'user'">
                                    <div class="md-role-option__icon">
                                        <i class="material-icons">person</i>
                                    </div>
                                    <div class="md-role-option__content">
                                        <div class="md-role-option__title">User</div>
                                        <div class="md-role-option__description">Regular user with standard permissions</div>
                                    </div>
                                    <div class="md-role-option__radio">
                                        <input 
                                            type="radio" 
                                            id="roleUser" 
                                            name="userRole" 
                                            value="user" 
                                            v-model="userForm.role" 
                                            required />
                                        <span class="md-radio-indicator"></span>
                                    </div>
                                </div>
                                
                                <div 
                                    class="md-role-option" 
                                    :class="{'md-role-option--selected': userForm.role === 'admin'}"
                                    @click="userForm.role = 'admin'">
                                    <div class="md-role-option__icon md-role-option__icon--admin">
                                        <i class="material-icons">admin_panel_settings</i>
                                    </div>
                                    <div class="md-role-option__content">
                                        <div class="md-role-option__title">Administrator</div>
                                        <div class="md-role-option__description">Full access to all system features</div>
                                    </div>
                                    <div class="md-role-option__radio">
                                        <input 
                                            type="radio" 
                                            id="roleAdmin" 
                                            name="userRole" 
                                            value="admin" 
                                            v-model="userForm.role" 
                                            required />
                                        <span class="md-radio-indicator"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="editMode" class="md-form-field">
                            <label for="userStatus" class="md-form-field__label">Account Status</label>
                            <div class="md-switch-field">
                                <div class="md-switch">
                                    <input 
                                        type="checkbox" 
                                        id="userStatus" 
                                        v-model="userForm.isActive" />
                                    <label for="userStatus" class="md-switch-toggle"></label>
                                </div>
                                <div class="md-switch-label">
                                    <span v-if="userForm.isActive" class="md-switch-status md-switch-status--active">
                                        <i class="material-icons">check_circle</i> Account Active
                                    </span>
                                    <span v-else class="md-switch-status md-switch-status--inactive">
                                        <i class="material-icons">cancel</i> Account Inactive
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="md-modal-actions">
                            <button type="button" class="md-button md-button--text" @click="closeUserModal">
                                Cancel
                            </button>
                            <button type="submit" class="md-button md-button--primary" :disabled="formProcessing">
                                <i class="material-icons" v-if="!formProcessing">save</i>
                                <i class="material-icons spin" v-else>sync</i>
                                <span>{{ formProcessing ? 'Saving...' : 'Save User' }}</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div v-if="showDeleteModal" class="md-modal">
            <div class="md-modal-backdrop" @click="cancelDelete"></div>
            <div class="md-modal-container md-modal-container--small">
                <div class="md-modal-header">
                    <h2 class="md-modal-title">
                        <i class="material-icons md-color-error">warning</i>
                        Confirm Delete
                    </h2>
                </div>
                
                <div class="md-modal-content">
                    <p class="md-modal-message">
                        Are you sure you want to delete user <strong>{{ userToDelete?.user_name }}</strong>?
                        This action cannot be undone.
                    </p>
                    
                    <div class="md-modal-actions">
                        <button class="md-button md-button--text" @click="cancelDelete">
                            Cancel
                        </button>
                        <button class="md-button md-button--danger" @click="deleteUser" :disabled="deleteProcessing">
                            <i class="material-icons" v-if="!deleteProcessing">delete</i>
                            <i class="material-icons spin" v-else>sync</i>
                            <span>{{ deleteProcessing ? 'Deleting...' : 'Delete' }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from "vuex";
import apiService from "../services/ApiService";

export default {
    name: 'UserManagement',

    data() {
        return {
            users: [],
            isLoading: true,
            showUserModal: false,
            showDeleteModal: false,
            editMode: false,
            canChangePassword: true,
            userToDelete: null,
            formProcessing: false,
            deleteProcessing: false,
            formErrors: [],
            userForm: {
                id: null,
                name: '',
                email: '',
                phone: '',
                password: '',
                role: 'user',
                isActive: true
            }
        }
    },

    computed: {
        ...mapState(["admin"]),
    },

    mounted() {
        // Check if admin is logged in
        if (!this.admin || this.admin.type !== 'admin') {
            console.log('Not authenticated as admin, redirecting to admin login');
            this.$router.push("/admin");
            return;
        }
        
        this.fetchUsers();
    },

    methods: {
        async fetchUsers() {
            try {
                this.isLoading = true;
                const users = await apiService.getAllUsers();
                this.users = users;
            } catch (error) {
                console.error('Error fetching users:', error);
                // Handle error appropriately
            } finally {
                this.isLoading = false;
            }
        },

        refreshUsers() {
            this.fetchUsers();
        },

        navigateToDashboard() {
            this.$router.push('/admin/dashboard');
        },

        openCreateUserModal() {
            this.editMode = false;
            this.formErrors = [];
            this.userForm = {
                id: null,
                name: '',
                email: '',
                phone: '',
                password: '',
                role: 'user',
                isActive: true
            };
            this.showUserModal = true;
        },

        editUser(user) {
            this.editMode = true;
            this.formErrors = [];
            this.canChangePassword = !this.isAdmin(user) || (this.admin && this.admin.id === user.user_id);
            
            this.userForm = {
                id: user.user_id,
                name: user.user_name,
                email: user.user_email,
                phone: user.user_phone || '',
                password: '',  // Don't pre-fill password
                role: user.role || 'user',
                isActive: user.is_active !== undefined ? user.is_active : true
            };
            
            this.showUserModal = true;
        },

        closeUserModal() {
            this.showUserModal = false;
            this.formErrors = [];
        },

        async saveUser() {
            this.formErrors = [];
            
            // Form validation
            if (!this.userForm.name) {
                this.formErrors.push('Name is required');
            }
            
            if (!this.userForm.email) {
                this.formErrors.push('Email is required');
            } else if (!this.isValidEmail(this.userForm.email)) {
                this.formErrors.push('Please enter a valid email address');
            }
            
            if (!this.editMode && !this.userForm.password) {
                this.formErrors.push('Password is required for new users');
            }
            
            if (this.formErrors.length > 0) {
                return;
            }
            
            this.formProcessing = true;
            
            try {
                const userData = {
                    user_name: this.userForm.name,
                    user_email: this.userForm.email,
                    user_phone: this.userForm.phone || null,
                    role: this.userForm.role,
                    is_active: this.userForm.isActive
                };
                
                // Only include password if it's provided (for new users or password changes)
                if (this.userForm.password) {
                    userData.user_password = this.userForm.password;
                }
                
                if (this.editMode) {
                    await apiService.updateUser(this.userForm.id, userData);
                } else {
                    await apiService.createUser(userData);
                }
                
                // Success!
                this.closeUserModal();
                this.fetchUsers(); // Refresh the user list
            } catch (error) {
                console.error('Error saving user:', error);
                if (error.response && error.response.data && error.response.data.error) {
                    this.formErrors.push(error.response.data.error);
                } else {
                    this.formErrors.push('An error occurred while saving the user. Please try again.');
                }
            } finally {
                this.formProcessing = false;
            }
        },

        async toggleUserStatus(user) {
            try {
                const newStatus = !user.is_active;
                await apiService.toggleUserStatus(user.user_id, newStatus);
                
                // Update the user in the local list
                const index = this.users.findIndex(u => u.user_id === user.user_id);
                if (index !== -1) {
                    this.users[index].is_active = newStatus;
                }
            } catch (error) {
                console.error('Error toggling user status:', error);
                // Handle error appropriately
            }
        },

        confirmDeleteUser(user) {
            this.userToDelete = user;
            this.showDeleteModal = true;
        },

        cancelDelete() {
            this.showDeleteModal = false;
            this.userToDelete = null;
        },

        async deleteUser() {
            if (!this.userToDelete) return;
            
            this.deleteProcessing = true;
            try {
                await apiService.deleteUser(this.userToDelete.user_id);
                
                // Remove from local list
                this.users = this.users.filter(user => user.user_id !== this.userToDelete.user_id);
                this.cancelDelete();
            } catch (error) {
                console.error('Error deleting user:', error);
                // Handle error appropriately
            } finally {
                this.deleteProcessing = false;
            }
        },

        getRoleClass(role) {
            switch (role) {
                case 'admin':
                    return 'md-status md-status--admin';
                default:
                    return 'md-status md-status--user';
            }
        },

        getStatusClass(isActive) {
            return isActive ? 'md-status md-status--active' : 'md-status md-status--inactive';
        },

        isAdmin(user) {
            return user.role === 'admin';
        },

        isValidEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }
    }
}
</script>

<style scoped>
/* These styles build on the existing admin dashboard styles */

.md-admin-users {
    margin-top: var(--md-sys-spacing-medium);
}

.md-admin-users__header {
    padding: var(--md-sys-spacing-medium);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    display: flex;
    align-items: center;
}

.md-admin-users__title {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
}

.md-admin-users__title i {
    margin-right: var(--md-sys-spacing-small);
}

.md-user-id {
    font-family: monospace;
    background-color: var(--md-sys-color-surface-variant);
    padding: 2px 6px;
    border-radius: var(--md-sys-shape-small);
}

.md-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: var(--md-sys-shape-small);
    font-size: 0.875rem;
    font-weight: 500;
}

.md-status--admin {
    background-color: var(--md-sys-color-tertiary-container);
    color: var(--md-sys-color-on-tertiary-container);
}

.md-status--user {
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface-variant);
}

.md-status--active {
    background-color: var(--md-sys-color-success-container);
    color: var(--md-sys-color-on-success-container);
}

.md-status--inactive {
    background-color: var(--md-sys-color-error-container);
    color: var(--md-sys-color-on-error-container);
}

.md-loading-container {
    display: flex;
    justify-content: center;
    padding: var(--md-sys-spacing-large);
}

.md-loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.md-loading-indicator i {
    font-size: 2rem;
    color: var(--md-sys-color-primary);
    margin-bottom: var(--md-sys-spacing-small);
}

.md-loading-indicator p {
    color: var(--md-sys-color-on-surface-variant);
}

/* Modal styles */
.md-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.md-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.md-modal-container {
    position: relative;
    width: 95%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    background-color: var(--md-sys-color-surface);
    border-radius: var(--md-sys-shape-medium);
    box-shadow: var(--md-sys-elevation-3);
    animation: modalFadeIn 0.3s ease-out;
}

.md-modal-container--small {
    max-width: 400px;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.md-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--md-sys-spacing-medium);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-modal-title {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
}

.md-modal-title i {
    margin-right: var(--md-sys-spacing-small);
}

.md-modal-content {
    padding: var(--md-sys-spacing-medium);
}

.md-modal-message {
    margin-bottom: var(--md-sys-spacing-medium);
    color: var(--md-sys-color-on-surface);
}

.md-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--md-sys-spacing-small);
    margin-top: var(--md-sys-spacing-medium);
}

.md-user-form {
    display: flex;
    flex-direction: column;
    gap: var(--md-sys-spacing-medium);
}

.spin {
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.md-color-error {
    color: var(--md-sys-color-error);
}

.md-form-error-container {
    padding: var(--md-sys-spacing-medium);
    background-color: var(--md-sys-color-error-container);
    border-radius: var(--md-sys-shape-small);
    margin-bottom: var(--md-sys-spacing-medium);
}

.md-form-error-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--md-sys-spacing-small);
}

.md-form-error-icon i {
    font-size: 1.5rem;
    color: var(--md-sys-color-on-error-container);
}

.md-form-error-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.md-form-error-item {
    padding: var(--md-sys-spacing-small);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-form-error-item:last-child {
    border-bottom: none;
}

.md-form-field {
    display: flex;
    flex-direction: column;
    gap: var(--md-sys-spacing-small);
}

.md-form-field__label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--md-sys-color-on-surface-variant);
}

.md-form-field__input-container {
    display: flex;
    align-items: center;
    padding: var(--md-sys-spacing-small);
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: var(--md-sys-shape-small);
}

.md-form-field__icon {
    margin-right: var(--md-sys-spacing-small);
    font-size: 1.25rem;
    color: var(--md-sys-color-on-surface-variant);
}

.md-form-field__input {
    flex-grow: 1;
    padding: var(--md-sys-spacing-small);
    border: none;
    border-radius: var(--md-sys-shape-small);
    font-size: 1rem;
    font-weight: 400;
    color: var(--md-sys-color-on-surface);
}

.md-form-field__input:focus {
    outline: none;
    border-color: var(--md-sys-color-primary);
}

.md-form-field__role-options {
    display: flex;
    gap: var(--md-sys-spacing-medium);
}

.md-role-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--md-sys-spacing-medium);
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: var(--md-sys-shape-small);
    cursor: pointer;
}

.md-role-option--selected {
    background-color: var(--md-sys-color-primary-container);
    border-color: var(--md-sys-color-primary);
}

.md-role-option__icon {
    font-size: 2rem;
    color: var(--md-sys-color-on-primary-container);
}

.md-role-option__icon--admin {
    color: var(--md-sys-color-on-tertiary-container);
}

.md-role-option__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--md-sys-spacing-small);
}

.md-role-option__title {
    font-size: 1rem;
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
}

.md-role-option__description {
    font-size: 0.875rem;
    color: var(--md-sys-color-on-surface-variant);
}

.md-role-option__radio {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: var(--md-sys-spacing-small);
}

.md-role-option__radio input[type="radio"] {
    display: none;
}

.md-radio-indicator {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: 50%;
    background-color: var(--md-sys-color-surface);
    transition: background-color 0.2s ease-out;
}

.md-role-option--selected .md-radio-indicator {
    background-color: var(--md-sys-color-primary);
}

.md-switch-field {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-small);
}

.md-switch {
    display: flex;
    align-items: center;
    position: relative;
}

.md-switch input[type="checkbox"] {
    display: none;
}

.md-switch-toggle {
    display: inline-block;
    width: 40px;
    height: 20px;
    border-radius: 10px;
    background-color: var(--md-sys-color-surface-variant);
    transition: background-color 0.2s ease-out;
    cursor: pointer;
    position: relative;
}

.md-switch-toggle::before {
    content: "";
    position: absolute;
    left: 2px;
    top: 2px;
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--md-sys-color-on-surface);
    transition: transform 0.2s ease-out;
}

.md-switch input[type="checkbox"]:checked + .md-switch-toggle {
    background-color: var(--md-sys-color-primary);
}

.md-switch input[type="checkbox"]:checked + .md-switch-toggle::before {
    transform: translateX(20px);
    background-color: var(--md-sys-color-on-primary);
}

.md-switch-label {
    font-size: 0.875rem;
    color: var(--md-sys-color-on-surface-variant);
}

.md-switch-status {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-small);
}

.md-switch-status--active {
    color: var(--md-sys-color-on-success-container);
}

.md-switch-status--inactive {
    color: var(--md-sys-color-on-error-container);
}
</style>
