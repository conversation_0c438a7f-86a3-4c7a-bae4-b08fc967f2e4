:root {
  /* Material You Color Tokens - Orange Theme */
  --md-sys-color-primary: #FF5722;
  --md-sys-color-primary-container: #FFCCBC;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-on-primary-container: #3E0500;
  --md-sys-color-secondary: #FF9800;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #FFE0B2;
  --md-sys-color-on-secondary-container: #874400;
  --md-sys-color-tertiary: #FF8A65;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #FFCCBC;
  --md-sys-color-on-tertiary-container: #3E0500;
  --md-sys-color-surface: #ffffff;
  --md-sys-color-surface-variant: #FFF8F6;
  --md-sys-color-surface-container: #F9F9F9;
  --md-sys-color-surface-bright: #FFFFFF;
  --md-sys-color-surface-dim: #F0F0F0;
  --md-sys-color-on-surface: #1D1B16;
  --md-sys-color-on-surface-variant: #49454E;
  --md-sys-color-outline: #79747E;
  --md-sys-color-outline-variant: #CAC4CF;
  --md-sys-color-background: #FDFBFF;
  --md-sys-color-error: #B3261E;
  --md-sys-color-on-error: #FFFFFF;
  --md-sys-color-error-container: #F9DEDC;
  --md-sys-color-on-error-container: #410E0B;

  /* Material You Typography */
  --md-sys-typescale-display-large-font: 'Google Sans', sans-serif;
  --md-sys-typescale-display-large-size: 3.5rem;
  --md-sys-typescale-display-large-weight: 400;
  --md-sys-typescale-display-large-line-height: 4rem;
  --md-sys-typescale-display-large-tracking: -0.0225em;

  --md-sys-typescale-display-medium-font: 'Google Sans', sans-serif;
  --md-sys-typescale-display-medium-size: 2.8rem;
  --md-sys-typescale-display-medium-weight: 400;
  --md-sys-typescale-display-medium-line-height: 3.25rem;
  --md-sys-typescale-display-medium-tracking: 0em;

  --md-sys-typescale-display-small-font: 'Google Sans', sans-serif;
  --md-sys-typescale-display-small-size: 2.25rem;
  --md-sys-typescale-display-small-weight: 400;
  --md-sys-typescale-display-small-line-height: 2.75rem;
  --md-sys-typescale-display-small-tracking: 0em;

  --md-sys-typescale-headline-large-font: 'Google Sans', sans-serif;
  --md-sys-typescale-headline-large-size: 2rem;
  --md-sys-typescale-headline-large-weight: 500;
  --md-sys-typescale-headline-large-line-height: 2.5rem;
  --md-sys-typescale-headline-large-tracking: 0em;

  --md-sys-typescale-headline-medium-font: 'Google Sans', sans-serif;
  --md-sys-typescale-headline-medium-size: 1.75rem;
  --md-sys-typescale-headline-medium-weight: 500;
  --md-sys-typescale-headline-medium-line-height: 2.25rem;
  --md-sys-typescale-headline-medium-tracking: 0em;

  --md-sys-typescale-headline-small-font: 'Google Sans', sans-serif;
  --md-sys-typescale-headline-small-size: 1.5rem;
  --md-sys-typescale-headline-small-weight: 500;
  --md-sys-typescale-headline-small-line-height: 2rem;
  --md-sys-typescale-headline-small-tracking: 0em;

  --md-sys-typescale-body-large-font: 'Roboto', sans-serif;
  --md-sys-typescale-body-large-size: 1rem;
  --md-sys-typescale-body-large-weight: 400;
  --md-sys-typescale-body-large-line-height: 1.5rem;
  --md-sys-typescale-body-large-tracking: 0.03125em;

  --md-sys-typescale-body-medium-font: 'Roboto', sans-serif;
  --md-sys-typescale-body-medium-size: 0.875rem;
  --md-sys-typescale-body-medium-weight: 400;
  --md-sys-typescale-body-medium-line-height: 1.25rem;
  --md-sys-typescale-body-medium-tracking: 0.0178571em;

  --md-sys-typescale-body-small-font: 'Roboto', sans-serif;
  --md-sys-typescale-body-small-size: 0.75rem;
  --md-sys-typescale-body-small-weight: 400;
  --md-sys-typescale-body-small-line-height: 1rem;
  --md-sys-typescale-body-small-tracking: 0.0333333em;

  --md-sys-typescale-label-large-font: 'Roboto', sans-serif;
  --md-sys-typescale-label-large-size: 0.875rem;
  --md-sys-typescale-label-large-weight: 500;
  --md-sys-typescale-label-large-line-height: 1.25rem;
  --md-sys-typescale-label-large-tracking: 0.0071429em;

  --md-sys-typescale-label-medium-font: 'Roboto', sans-serif;
  --md-sys-typescale-label-medium-size: 0.75rem;
  --md-sys-typescale-label-medium-weight: 500;
  --md-sys-typescale-label-medium-line-height: 1rem;
  --md-sys-typescale-label-medium-tracking: 0.0416667em;

  --md-sys-typescale-label-small-font: 'Roboto', sans-serif;
  --md-sys-typescale-label-small-size: 0.6875rem;
  --md-sys-typescale-label-small-weight: 500;
  --md-sys-typescale-label-small-line-height: 0.875rem;
  --md-sys-typescale-label-small-tracking: 0.0454545em;

  /* Material You Shape Scale */
  --md-sys-shape-corner-none: 0;
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
  --md-sys-shape-corner-full: 999px;

  /* Material You Elevation */
  --md-elevation-level0: none;
  --md-elevation-level1: 0 1px 2px rgba(0,0,0,0.3), 0 1px 3px 1px rgba(0,0,0,0.15);
  --md-elevation-level2: 0 2px 4px rgba(0,0,0,0.3), 0 3px 6px rgba(0,0,0,0.15);
  --md-elevation-level3: 0 4px 8px rgba(0,0,0,0.3), 0 6px 10px rgba(0,0,0,0.15);
  --md-elevation-level4: 0 6px 10px rgba(0,0,0,0.3), 0 8px 12px rgba(0,0,0,0.15);
  --md-elevation-level5: 0 8px 12px rgba(0,0,0,0.3), 0 12px 16px rgba(0,0,0,0.15);

  /* Material You Spacing */
  --md-sys-spacing-track: 4px;
  --md-sys-spacing-extra-small: 4px;
  --md-sys-spacing-small: 8px;
  --md-sys-spacing-medium: 16px;
  --md-sys-spacing-large: 24px;
  --md-sys-spacing-extra-large: 32px;
  --md-sys-spacing-extra-extra-large: 48px;
  --md-sys-spacing-extra-extra-extra-large: 64px;

  /* Material You Motion */
  --md-sys-motion-duration-short1: 100ms;
  --md-sys-motion-duration-short2: 200ms;
  --md-sys-motion-duration-short3: 250ms;
  --md-sys-motion-duration-short4: 300ms;
  --md-sys-motion-duration-medium1: 400ms;
  --md-sys-motion-duration-medium2: 500ms;
  --md-sys-motion-duration-medium3: 600ms;
  --md-sys-motion-duration-medium4: 700ms;
  --md-sys-motion-duration-long1: 800ms;
  --md-sys-motion-duration-long2: 900ms;
  --md-sys-motion-duration-long3: 1000ms;
  --md-sys-motion-duration-long4: 1100ms;
  --md-sys-motion-duration-extra-long1: 1200ms;
  --md-sys-motion-duration-extra-long2: 1300ms;
  --md-sys-motion-duration-extra-long3: 1400ms;
  --md-sys-motion-duration-extra-long4: 1500ms;
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0.0, 0, 1.0);
  --md-sys-motion-easing-standard-accelerate: cubic-bezier(0.3, 0.0, 1.0, 1.0);
  --md-sys-motion-easing-standard-decelerate: cubic-bezier(0.0, 0.0, 0.0, 1.0);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0.0, 0, 1.0);
  --md-sys-motion-easing-emphasized-accelerate: cubic-bezier(0.3, 0.0, 0.8, 0.15);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1.0);
}

/* Dark theme overrides */
@media (prefers-color-scheme: dark) {
  :root {
    --md-sys-color-primary: #FF8A65;
    --md-sys-color-primary-container: #872100;
    --md-sys-color-on-primary: #FFFFFF;
    --md-sys-color-on-primary-container: #FFDBCF;
    --md-sys-color-secondary: #FFB74D;
    --md-sys-color-on-secondary: #FFFFFF;
    --md-sys-color-secondary-container: #874400;
    --md-sys-color-on-secondary-container: #FFDDB0;
    --md-sys-color-tertiary: #FFAB91;
    --md-sys-color-on-tertiary: #FFFFFF;
    --md-sys-color-tertiary-container: #872100;
    --md-sys-color-on-tertiary-container: #FFDBD0;
    --md-sys-color-surface: #1D1B16;
    --md-sys-color-surface-variant: #49454F;
    --md-sys-color-surface-container: #2E2B25;
    --md-sys-color-surface-bright: #3B3730;
    --md-sys-color-surface-dim: #121212;
    --md-sys-color-on-surface: #E6E1D9;
    --md-sys-color-on-surface-variant: #CAC4CF;
    --md-sys-color-outline: #938F99;
    --md-sys-color-outline-variant: #49454F;
    --md-sys-color-background: #1A1C1E;
    --md-elevation-level1: 0 1px 3px rgba(0,0,0,0.5), 0 1px 2px rgba(0,0,0,0.7);
    --md-elevation-level2: 0 2px 6px rgba(0,0,0,0.5), 0 2px 4px rgba(0,0,0,0.7);
    --md-elevation-level3: 0 4px 12px rgba(0,0,0,0.5), 0 4px 8px rgba(0,0,0,0.7);
  }
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base styles */
body {
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  line-height: var(--md-sys-typescale-body-large-line-height);
  font-weight: var(--md-sys-typescale-body-large-weight);
  letter-spacing: var(--md-sys-typescale-body-large-tracking);
  color: var(--md-sys-color-on-surface);
  background-color: var(--md-sys-color-background);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Common Typography */
h1 {
  font-family: var(--md-sys-typescale-display-large-font);
  font-size: var(--md-sys-typescale-display-large-size);
  font-weight: var(--md-sys-typescale-display-large-weight);
  line-height: var(--md-sys-typescale-display-large-line-height);
  letter-spacing: var(--md-sys-typescale-display-large-tracking);
  margin-bottom: var(--md-sys-spacing-medium);
}

h2 {
  font-family: var(--md-sys-typescale-display-medium-font);
  font-size: var(--md-sys-typescale-display-medium-size);
  font-weight: var(--md-sys-typescale-display-medium-weight);
  line-height: var(--md-sys-typescale-display-medium-line-height);
  letter-spacing: var(--md-sys-typescale-display-medium-tracking);
  margin-bottom: var(--md-sys-spacing-medium);
}

h3 {
  font-family: var(--md-sys-typescale-display-small-font);  
  font-size: var(--md-sys-typescale-display-small-size);
  font-weight: var(--md-sys-typescale-display-small-weight);
  line-height: var(--md-sys-typescale-display-small-line-height);
  letter-spacing: var(--md-sys-typescale-display-small-tracking);
  margin-bottom: var(--md-sys-spacing-small);
}

h4 {
  font-family: var(--md-sys-typescale-headline-large-font);
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  line-height: var(--md-sys-typescale-headline-large-line-height);
  letter-spacing: var(--md-sys-typescale-headline-large-tracking);
  margin-bottom: var(--md-sys-spacing-small);
}

h5 {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
  letter-spacing: var(--md-sys-typescale-headline-medium-tracking);
  margin-bottom: var(--md-sys-spacing-small);
}

h6 {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  letter-spacing: var(--md-sys-typescale-headline-small-tracking);
  margin-bottom: var(--md-sys-spacing-small);
}

p {
  margin-bottom: var(--md-sys-spacing-medium);
}

a {
  color: var(--md-sys-color-primary);
  text-decoration: none;
  transition: color var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

a:hover {
  color: var(--md-sys-color-secondary);
}

/* Button Base Styles */
.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  letter-spacing: var(--md-sys-typescale-label-large-tracking);
  text-transform: uppercase;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  position: relative;
  overflow: hidden;
  min-height: 40px;
  min-width: 64px;
}

.md-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: currentColor;
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-button:hover::after {
  opacity: 0.08;
}

.md-button:active::after {
  opacity: 0.12;
}

.md-button:focus-visible {
  outline: 2px solid var(--md-sys-color-outline);
  outline-offset: 2px;
}

.md-button--primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-elevation-level1);
}

.md-button--primary:hover {
  box-shadow: var(--md-elevation-level2);
}

.md-button--primary:active {
  box-shadow: var(--md-elevation-level0);
}

.md-button--tonal {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.md-button--text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-small);
}

.md-button--outline {
  border: 1px solid var(--md-sys-color-outline);
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.md-button--icon {
  width: 40px;
  height: 40px;
  min-width: 40px;
  padding: 0;
  border-radius: var(--md-sys-shape-corner-full);
}

.md-fab {
  position: fixed;
  right: var(--md-sys-spacing-large);
  bottom: var(--md-sys-spacing-extra-large);
  width: 56px;
  height: 56px;
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-elevation-level3);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-fab:hover {
  box-shadow: var(--md-elevation-level4);
  transform: translateY(-2px);
}

.md-fab:active {
  box-shadow: var(--md-elevation-level2);
  transform: translateY(0);
}

.md-fab .material-icons-round {
  font-size: 24px;
}

/* Chip Base Styles */
.md-chip {
  display: inline-flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  border-radius: var(--md-sys-shape-corner-full);
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  letter-spacing: var(--md-sys-typescale-label-large-tracking);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  cursor: pointer;
  user-select: none;
  height: 32px;
  box-shadow: var(--md-elevation-level0);
}

.md-chip:hover {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  box-shadow: var(--md-elevation-level1);
}

.md-chip--selected {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-elevation-level1);
}

.md-chip .material-icons-round {
  font-size: 18px;
}

/* Card Styles */
.md-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-elevation-level1);
  overflow: hidden;
  transition: box-shadow var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard),
              transform var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-card:hover {
  box-shadow: var(--md-elevation-level2);
  transform: translateY(-2px);
}

.md-card__media {
  position: relative;
  overflow: hidden;
}

.md-card__media img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform var(--md-sys-motion-duration-medium1) var(--md-sys-motion-easing-standard);
}

.md-card:hover .md-card__media img {
  transform: scale(1.05);
}

.md-card__content {
  padding: var(--md-sys-spacing-medium);
}

.md-card__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  margin-bottom: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface);
}

.md-card__subtitle {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: var(--md-sys-spacing-medium);
}

.md-card__actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  gap: var(--md-sys-spacing-small);
}

/* Top App Bar */
.md-top-app-bar {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  box-shadow: var(--md-elevation-level2);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: box-shadow var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard),
              background-color var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-top-app-bar__row {
  display: flex;
  align-items: center;
  padding: var(--md-sys-spacing-medium) var(--md-sys-spacing-large);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.md-top-app-bar__section {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
}

.md-top-app-bar__section--end {
  margin-left: auto;
}

.md-top-app-bar__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-top-app-bar__logo {
  height: 32px;
  width: auto;
}

/* Bottom Navigation */
.md-bottom-navigation {
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 64px;
  background-color: var(--md-sys-color-surface);
  box-shadow: var(--md-elevation-level2);
  z-index: 100;
  padding: var(--md-sys-spacing-small) 0;
}

.md-bottom-navigation__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  padding: var(--md-sys-spacing-small) 0;
  transition: color var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  position: relative;
}

.md-bottom-navigation__item.router-link-active {
  color: var(--md-sys-color-primary);
}

.md-bottom-navigation__item.router-link-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 4px;
  background-color: var(--md-sys-color-primary);
  border-radius: var(--md-sys-shape-corner-full) var(--md-sys-shape-corner-full) 0 0;
}

.md-bottom-navigation__item .material-icons-round {
  font-size: 24px;
  margin-bottom: var(--md-sys-spacing-extra-small);
}

.md-bottom-navigation__item span {
  font-size: var(--md-sys-typescale-label-medium-size);
  font-weight: var(--md-sys-typescale-label-medium-weight);
}

/* Search Bar */
.md-search-bar {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.md-search-bar__container {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-small);
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-full);
  padding: var(--md-sys-spacing-small) var(--md-sys-spacing-medium);
  transition: background-color var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard),
              box-shadow var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-search-bar__container:focus-within {
  background-color: var(--md-sys-color-surface);
  box-shadow: var(--md-elevation-level1);
}

.md-search-bar__container input {
  flex: 1;
  border: none;
  background: transparent;
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
  outline: none;
}

.md-search-bar__container input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
}

.md-search-bar__container .material-icons-round {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 20px;
}

/* Section Header */
.md-section-header {
  margin-bottom: var(--md-sys-spacing-large);
  text-align: center;
}

.md-section-header__title {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  color: var(--md-sys-color-on-surface);
  margin-bottom: var(--md-sys-spacing-extra-small);
}

.md-section-header__subtitle {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
}