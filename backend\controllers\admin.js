// Admin controller for authentication and management functions
import db from "../config/database.js";

// Hard-coded admin credentials for security (in production, use a database and proper encryption)
const ADMIN_CREDENTIALS = {
    username: "admin",
    password: "123456"
};

// Admin login
export const adminLogin = (req, res) => {
    const { username, password } = req.body;
    
    // First check hardcoded credentials
    if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
        // Send success with admin info (without password)
        return res.json({
            success: true,
            admin: {
                username: ADMIN_CREDENTIALS.username,
                type: "admin"
            }
        });
    }
    
    // If hardcoded credentials don't match, check database for admin users
    db.query("SELECT user_id, user_name, user_email, user_password, role FROM user WHERE user_email = ? AND role = 'admin'", 
        [username], (err, results) => {
            if (err) {
                console.log(err);
                return res.status(500).json({
                    success: false,
                    message: "Server error",
                    error: err
                });
            }
            
            // If user found and password matches
            if (results.length > 0 && results[0].user_password === password) {
                const user = results[0];
                return res.json({
                    success: true,
                    admin: {
                        username: user.user_email,
                        userId: user.user_id,
                        type: "admin"
                    }
                });
            } else {
                // Authentication failed
                return res.status(401).json({
                    success: false,
                    message: "Invalid username or password"
                });
            }
        });
};

// Admin check (simple validation endpoint)
export const checkAdmin = (req, res) => {
    // This could be expanded to validate admin tokens/sessions in a real app
    res.json({
        success: true,
        message: "Admin authenticated"
    });
};

// Get all users (admin function)
export const getAllUsers = (req, res) => {
    db.query("SELECT user_id, user_name, user_email, user_phone, role, is_active FROM user", (err, results) => {
        if (err) {
            console.log(err);
            return res.status(500).json({
                success: false,
                message: "Server error",
                error: err
            });
        }
        
        // Return the users array
        res.json(results);
    });
};

// Create a new user (admin function)
export const createUser = (req, res) => {
    const {
        user_name,
        user_email,
        user_password,
        user_phone,
        role,
        is_active
    } = req.body;
    
    // Validate required fields
    if (!user_name || !user_email || !user_password) {
        return res.status(400).json({
            success: false,
            message: "Name, email and password are required"
        });
    }
    
    // Check if email already exists
    db.query("SELECT user_id FROM user WHERE user_email = ?", [user_email], (err, results) => {
        if (err) {
            console.log(err);
            return res.status(500).json({
                success: false,
                message: "Server error",
                error: err
            });
        }
        
        // If email exists, return error
        if (results.length > 0) {
            return res.status(400).json({
                success: false,
                message: "Email already in use",
                error: "Email already exists"
            });
        }
        
        // Create user data object
        const userData = {
            user_name,
            user_email,
            user_password,
            user_phone: user_phone || null,
            role: role || 'user',
            is_active: is_active !== undefined ? is_active : true
        };
        
        // Insert new user
        db.query("INSERT INTO user SET ?", userData, (insertErr, insertResult) => {
            if (insertErr) {
                console.log(insertErr);
                return res.status(500).json({
                    success: false,
                    message: "Failed to create user",
                    error: insertErr
                });
            }
            
            // Return success with the created user (without password)
            const { user_password, ...userWithoutPassword } = userData;
            res.status(201).json({
                success: true,
                message: "User created successfully",
                user: {
                    user_id: insertResult.insertId,
                    ...userWithoutPassword
                }
            });
        });
    });
};

// Update a user (admin function)
export const updateUser = (req, res) => {
    const userId = req.params.id;
    const {
        user_name,
        user_email,
        user_password,
        user_phone,
        role,
        is_active
    } = req.body;
    
    // Validate required fields
    if (!user_name || !user_email) {
        return res.status(400).json({
            success: false,
            message: "Name and email are required"
        });
    }
    
    // Check if email already exists for another user
    db.query("SELECT user_id FROM user WHERE user_email = ? AND user_id != ?", [user_email, userId], (err, results) => {
        if (err) {
            console.log(err);
            return res.status(500).json({
                success: false,
                message: "Server error",
                error: err
            });
        }
        
        // If email exists for another user, return error
        if (results.length > 0) {
            return res.status(400).json({
                success: false,
                message: "Email already in use by another user",
                error: "Email already exists"
            });
        }
        
        // Create user data object
        const userData = {
            user_name,
            user_email,
            user_phone: user_phone || null,
            role: role || 'user',
            is_active: is_active !== undefined ? is_active : true
        };
        
        // Add password only if provided
        if (user_password) {
            userData.user_password = user_password;
        }
        
        // Update user
        db.query("UPDATE user SET ? WHERE user_id = ?", [userData, userId], (updateErr, updateResult) => {
            if (updateErr) {
                console.log(updateErr);
                return res.status(500).json({
                    success: false,
                    message: "Failed to update user",
                    error: updateErr
                });
            }
            
            if (updateResult.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: "User not found"
                });
            }
            
            // Return success with the updated user (without password)
            const { user_password, ...userWithoutPassword } = userData;
            res.json({
                success: true,
                message: "User updated successfully",
                user: {
                    user_id: parseInt(userId),
                    ...userWithoutPassword
                }
            });
        });
    });
};

// Delete a user (admin function)
export const deleteUser = (req, res) => {
    const userId = req.params.id;
    
    // Don't allow deletion of admin users as a safety measure
    db.query("SELECT role FROM user WHERE user_id = ?", [userId], (checkErr, checkResults) => {
        if (checkErr) {
            console.log(checkErr);
            return res.status(500).json({
                success: false,
                message: "Server error",
                error: checkErr
            });
        }
        
        if (checkResults.length === 0) {
            return res.status(404).json({
                success: false,
                message: "User not found"
            });
        }
        
        // If user is an admin, don't allow deletion
        if (checkResults[0].role === 'admin') {
            return res.status(403).json({
                success: false,
                message: "Cannot delete admin users"
            });
        }
        
        // Delete the user
        db.query("DELETE FROM user WHERE user_id = ?", [userId], (deleteErr, deleteResult) => {
            if (deleteErr) {
                console.log(deleteErr);
                return res.status(500).json({
                    success: false,
                    message: "Failed to delete user",
                    error: deleteErr
                });
            }
            
            if (deleteResult.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: "User not found"
                });
            }
            
            res.json({
                success: true,
                message: "User deleted successfully"
            });
        });
    });
};

// Toggle user status (active/inactive) - admin function
export const toggleUserStatus = (req, res) => {
    const userId = req.params.id;
    const { isActive } = req.body;
    
    // Update user status
    db.query(
        "UPDATE user SET is_active = ? WHERE user_id = ?",
        [isActive, userId],
        (err, result) => {
            if (err) {
                console.log(err);
                return res.status(500).json({
                    success: false,
                    message: "Failed to update user status",
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: "User not found"
                });
            }
            
            res.json({
                success: true,
                message: `User ${isActive ? 'activated' : 'deactivated'} successfully`
            });
        }
    );
};

// Update user role - admin function
export const updateUserRole = (req, res) => {
    const userId = req.params.id;
    const { role } = req.body;
    
    // Validate role
    if (!role || (role !== 'user' && role !== 'admin')) {
        return res.status(400).json({
            success: false,
            message: "Invalid role. Must be 'user' or 'admin'"
        });
    }
    
    // Update user role
    db.query(
        "UPDATE user SET role = ? WHERE user_id = ?",
        [role, userId],
        (err, result) => {
            if (err) {
                console.log(err);
                return res.status(500).json({
                    success: false,
                    message: "Failed to update user role",
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: "User not found"
                });
            }
            
            res.json({
                success: true,
                message: `User role updated to ${role} successfully`
            });
        }
    );
};
