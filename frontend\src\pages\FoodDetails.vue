<template>
  <!-- Loading Overlay -->
  <div v-if="loading" class="md-loading-overlay">
    <div class="md-loading-content">
      <span class="material-icons md-loading-icon">restaurant_menu</span>
      <p>Loading food details...</p>
    </div>
  </div>

  <!-- Food Details Section -->
  <template v-else-if="food">
    <div class="md-food-details">
      <div class="md-container">
        <!-- Back Button -->
        <button @click="$router.go(-1)" class="md-button md-button--icon md-button--back">
          <span class="material-icons">arrow_back</span>
          <span>Back to Menu</span>
        </button>

        <div class="md-food-details__content">
          <!-- Left Column: Image -->
          <div class="md-food-details__image-container">
            <div class="md-food-details__image">
              <img v-if="getImageSrc(food)" :src="getImageSrc(food)" :alt="food.food_name" @error="handleImageError">
              <div v-else class="md-food-details__placeholder">
                <span class="material-icons">image</span>
                <span>No image available</span>
              </div>
            </div>

            <!-- Food Categories -->
            <div class="md-food-details__categories">
              <span class="md-chip" v-if="food.food_category">{{ food.food_category }}</span>
              <span class="md-chip" v-if="food.food_type">{{ food.food_type }}</span>
              <span class="md-chip md-chip--highlight" v-if="food.food_status">{{ food.food_status }}</span>
            </div>
          </div>

          <!-- Right Column: Details -->
          <div class="md-food-details__info">
            <h1 class="md-food-details__title">{{ food.food_name }}</h1>

            <!-- Rating -->
            <div class="md-rating md-rating--large">
              <div class="md-rating__stars">
                <span
                  class="material-icons"
                  v-for="n in 5"
                  :key="n"
                  :class="{'md-rating__star--filled': n <= Math.floor(parseFloat(food.food_star))}"
                >star</span>
              </div>
              <span class="md-rating__count">({{ food.food_vote }} reviews)</span>
            </div>

            <!-- Price Section -->
            <div class="md-food-details__price">
              <span class="md-food-details__current-price">
                ${{ parseFloat(food.food_price) - parseFloat(food.food_discount) }}
              </span>
              <span v-if="parseFloat(food.food_discount) > 0" class="md-food-details__original-price">
                ${{ parseFloat(food.food_price) }}
              </span>
              <span v-if="parseFloat(food.food_discount) > 0" class="md-food-details__discount">
                Save ${{ parseFloat(food.food_discount) }}
              </span>
            </div>

            <!-- Description -->
            <p class="md-food-details__description">{{ food.food_desc }}</p>

            <!-- Add to Cart Section -->
            <div class="md-food-details__actions">
              <div class="md-quantity-field">
                <label>
                  <span class="md-quantity-field__label">Quantity</span>
                  <div class="md-quantity-field__controls">
                    <button
                      class="md-button md-button--icon"
                      @click="quantity > 1 && quantity--"
                    >
                      <span class="material-icons">remove</span>
                    </button>
                    <input
                      type="number"
                      v-model="quantity"
                      min="1"
                      class="md-quantity-field__input"
                      @change="onQuantityChange($event)"
                    >
                    <button
                      class="md-button md-button--icon"
                      @click="quantity++"
                    >
                      <span class="material-icons">add</span>
                    </button>
                  </div>
                </label>
              </div>

              <button
                v-if="user"
                class="md-button md-button--primary md-button--large"
                @click="addToCart"
                :disabled="addingToCart"
              >
                <span v-if="addingToCart">
                  <span class="material-icons md-button__icon md-button__icon--spin">refresh</span>
                  Adding...
                </span>
                <span v-else>
                  <span class="material-icons md-button__icon">add_shopping_cart</span>
                  Add to Cart
                </span>
              </button>

              <router-link
                v-else
                to="/login"
                class="md-button md-button--primary md-button--large"
              >
                <span class="material-icons md-button__icon">login</span>
                Login to Order
              </router-link>
            </div>

            <!-- Share Buttons -->
            <div class="md-food-details__share">
              <h3 class="md-food-details__share-title">Share this dish</h3>
              <div class="md-food-details__share-buttons">
                <button class="md-button md-button--icon md-button--share" @click="shareItem('facebook')">
                  <span class="material-icons">facebook</span>
                </button>
                <button class="md-button md-button--icon md-button--share" @click="shareItem('twitter')">
                  <span class="material-icons">twitter</span>
                </button>
                <button class="md-button md-button--icon md-button--share" @click="shareItem('pinterest')">
                  <span class="material-icons">pin_drop</span>
                </button>
                <button class="md-button md-button--icon md-button--share" @click="shareItem('email')">
                  <span class="material-icons">email</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reviews Section -->
    <div class="md-container">
      <reviews-section
        :food-id="foodId"
        @notification="showNotification"
      ></reviews-section>
    </div>

    <!-- Related Items Section -->
    <div class="md-container">
      <div class="md-related-items">
        <h2 class="md-related-items__title">You might also like</h2>

        <div v-if="loadingRelated" class="md-loading-text">
          <span class="material-icons md-loading-icon md-loading-icon--small">refresh</span>
          Loading related items...
        </div>

        <div v-else-if="relatedItems.length === 0" class="md-empty-state">
          <span class="material-icons">search_off</span>
          <p>No related items found</p>
        </div>

        <div v-else class="md-related-items__grid">
          <div
            v-for="item in relatedItems"
            :key="item.food_id"
            class="md-food-card"
            @click="goToFoodDetails(item.food_id)"
          >
            <div class="md-food-card__image">
              <img
                v-if="getImageSrc(item)"
                :src="getImageSrc(item)"
                :alt="item.food_name"
                @error="handleImageError"
              >
              <div v-else class="md-food-card__placeholder">
                <span class="material-icons">image</span>
              </div>
            </div>

            <div class="md-food-card__content">
              <h3 class="md-food-card__title">{{ item.food_name }}</h3>

              <div class="md-food-card__price">
                <span class="md-food-card__current-price">
                  ${{ parseFloat(item.food_price) - parseFloat(item.food_discount) }}
                </span>
                <span v-if="parseFloat(item.food_discount) > 0" class="md-food-card__original-price">
                  ${{ parseFloat(item.food_price) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <!-- Error State -->
  <div v-else class="md-error-state">
    <div class="md-container">
      <span class="material-icons md-error-state__icon">error_outline</span>
      <h2 class="md-error-state__title">Food item not found</h2>
      <p class="md-error-state__message">The food item you're looking for might not exist or has been removed.</p>
      <router-link to="/menu" class="md-button md-button--primary">
        <span class="material-icons md-button__icon">restaurant_menu</span>
        Back to Menu
      </router-link>
    </div>
  </div>

  <!-- Notification -->
  <div
    v-if="notification.show"
    class="md-notification"
    :class="`md-notification--${notification.type}`"
  >
    <span class="material-icons md-notification__icon">
      {{ notificationIcon }}
    </span>
    <span class="md-notification__message">{{ notification.message }}</span>
    <button class="md-notification__close" @click="notification.show = false">
      <span class="material-icons">close</span>
    </button>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import axios from 'axios';
import ReviewsSection from '../components/ReviewsSection.vue';
import apiService from '../services/ApiService';

export default {
  name: 'FoodDetails',

  components: {
    ReviewsSection
  },

  props: {
    id: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      foodId: null,
      food: null,
      loading: true,
      quantity: 1,
      addingToCart: false,
      relatedItems: [],
      loadingRelated: true,
      notification: {
        show: false,
        type: 'success',
        message: ''
      }
    };
  },

  computed: {
    ...mapState(['user']),

    notificationIcon() {
      const icons = {
        success: 'check_circle',
        error: 'error',
        warning: 'warning',
        info: 'info'
      };

      return icons[this.notification.type] || 'info';
    }
  },

  watch: {
    '$route.params.id': {
      immediate: true,
      handler(newId) {
        console.log('=== FOOD DETAILS DEBUG ===');
        console.log('Route params:', this.$route.params);
        console.log('New ID from route:', newId);
        console.log('Type of newId:', typeof newId);
        console.log('========================');

        if (newId && newId !== 'undefined') {
          this.foodId = newId;
          this.loadFoodDetails();
        } else {
          console.error('Invalid food ID from route:', newId);
          // Redirect back to menu if ID is invalid
          this.$router.push('/menu');
        }
      }
    }
  },

  methods: {
    async loadFoodDetails() {
      this.loading = true;

      try {
        console.log('Loading food details for ID:', this.foodId);

        // Load food details
        this.food = await apiService.getFoodById(this.foodId);

        console.log('Loaded food data:', this.food);

        if (!this.food) {
          console.error('No food data returned for ID:', this.foodId);
          this.showNotification({
            type: 'error',
            message: 'Food item not found'
          });
          // Redirect back to menu after a delay
          setTimeout(() => {
            this.$router.push('/menu');
          }, 2000);
          return;
        }

        // Load related items (items in the same category)
        this.loadRelatedItems();
      } catch (error) {
        console.error('Error loading food details:', error);
        this.food = null;
        this.showNotification({
          type: 'error',
          message: 'Failed to load food details'
        });
        // Redirect back to menu after a delay
        setTimeout(() => {
          this.$router.push('/menu');
        }, 2000);
      } finally {
        this.loading = false;
      }
    },

    async loadRelatedItems() {
      if (!this.food || !this.food.food_category) {
        this.relatedItems = [];
        this.loadingRelated = false;
        return;
      }

      this.loadingRelated = true;

      try {
        // Get all foods
        const allFoods = await apiService.getFoods();

        // Filter related items (same category, excluding current item)
        this.relatedItems = allFoods
          .filter(item =>
            item.food_category === this.food.food_category &&
            item.food_id !== this.food.food_id
          )
          .slice(0, 4); // Limit to 4 related items
      } catch (error) {
        console.error('Error loading related items:', error);
        this.relatedItems = [];
      } finally {
        this.loadingRelated = false;
      }
    },

    onQuantityChange(event) {
      if (event.target.value < 1) {
        this.quantity = 1;
      }
    },

    async addToCart() {
      if (!this.user || this.addingToCart) return;

      this.addingToCart = true;

      try {
        // First check if item exists in cart
        const foodId = this.food.food_id;

        try {
          const existingItem = await axios.get(`/api/cartItem/${this.user.user_id}/${foodId}`);

          if (existingItem.data && existingItem.data.length > 0) {
            // Update quantity if already in cart
            const newQty = parseInt(existingItem.data[0].item_qty) + this.quantity;

            await axios.put("/api/cartItem/", {
              user_id: this.user.user_id,
              food_id: parseInt(foodId),
              item_qty: newQty
            });
          } else {
            // Add new item if not in cart
            await axios.post("/api/cartItem", {
              user_id: this.user.user_id,
              food_id: parseInt(foodId),
              item_qty: this.quantity
            });
          }

          // Show success notification
          this.showNotification({
            type: 'success',
            message: `${this.food.food_name} added to your cart!`
          });

          // Reset quantity
          this.quantity = 1;
        } catch (error) {
          console.log('Item not found in cart, adding new item');

          // Item doesn't exist in cart, add it
          await axios.post("/api/cartItem", {
            user_id: this.user.user_id,
            food_id: parseInt(foodId),
            item_qty: this.quantity
          });

          // Show success notification
          this.showNotification({
            type: 'success',
            message: `${this.food.food_name} added to your cart!`
          });

          // Reset quantity
          this.quantity = 1;
        }
      } catch (error) {
        console.error('Error adding item to cart:', error);

        // Show error notification
        this.showNotification({
          type: 'error',
          message: 'Failed to add item to cart. Please try again.'
        });
      } finally {
        this.addingToCart = false;
      }
    },

    goToFoodDetails(foodId) {
      // Only navigate if it's a different food item
      if (foodId !== this.foodId) {
        this.$router.push(`/food/${foodId}`);
      }
    },

    shareItem(platform) {
      if (!this.food) return;

      const url = window.location.href;
      const title = `Check out ${this.food.food_name} at our restaurant!`;

      let shareUrl = '';

      switch (platform) {
        case 'facebook':
          shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
          break;
        case 'twitter':
          shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`;
          break;
        case 'pinterest':
          shareUrl = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&description=${encodeURIComponent(title)}`;
          break;
        case 'email':
          shareUrl = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(url)}`;
          break;
      }

      if (shareUrl) {
        window.open(shareUrl, '_blank');
      }
    },

    showNotification({ type, message }) {
      this.notification = {
        show: true,
        type: type || 'info',
        message
      };

      // Auto-dismiss after 5 seconds
      setTimeout(() => {
        this.notification.show = false;
      }, 5000);
    },

    // Helper method to get image source with fallbacks
    getImageSrc(food) {
      if (!food) {
        return require('../assets/images/nachos-img.png');
      }

      // Handle image path formats from both API and mock data
      const imageName = food.food_image || food.food_src || food.image;

      // If it's a full URL, use it directly
      if (imageName && (imageName.startsWith('http://') || imageName.startsWith('https://'))) {
        return imageName;
      }

      // Handle cases where database returns 'food-img.png' which doesn't exist
      if (imageName === 'food-img.png') {
        // Get category-based default instead
        const category = (food.food_category || food.category || 'other').toLowerCase();
        return this.getCategoryDefaultImage(category);
      }

      // Use a safer approach with a default fallback image
      if (imageName) {
        try {
          return require(`../assets/images/${imageName}`);
        } catch (error) {
          // Image not found, will select category-based default
          console.log('Image not found:', imageName);
        }
      }

      // Select a default image based on category
      const category = (food.food_category || food.category || 'other').toLowerCase();
      return this.getCategoryDefaultImage(category);
    },

    // Helper method to get default images by category
    getCategoryDefaultImage(category) {
      // These are guaranteed to exist in our assets
      if (category.includes('taco')) {
        return require('../assets/images/taco-img.png');
      } else if (category.includes('burrito')) {
        return require('../assets/images/burrito-img.png');
      } else if (category.includes('nacho')) {
        return require('../assets/images/nachos-img.png');
      } else if (category.includes('side') || category.includes('salad')) {
        return require('../assets/images/salad-img.png');
      } else if (category.includes('dessert')) {
        return require('../assets/images/dessert-img.png');
      } else if (category.includes('coca') || category.includes('drink')) {
        return require('../assets/images/coca-img.png');
      }

      // Default fallback image for any other category
      return require('../assets/images/nachos-img.png');
    },

    // Handle image loading errors
    handleImageError(event) {
      console.warn('Image failed to load, using fallback');
      event.target.src = require('../assets/images/nachos-img.png');
    }
  }
};
</script>

<style scoped>
.food-details-page {
  padding-top: 2rem;
  padding-bottom: 4rem;
}

.md-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.md-loading-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.md-loading-content {
  text-align: center;
  color: #757575;
}

.md-loading-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
  animation: spin 2s linear infinite;
  color: #1976d2;
}

.md-loading-icon--small {
  font-size: 1.5rem;
  margin-right: 0.5rem;
  display: inline-block;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.md-loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #757575;
  padding: 2rem 0;
}

.md-button--back {
  margin-bottom: 1.5rem;
  color: #666;
  background-color: transparent;
  padding: 0.5rem 1rem;
  display: inline-flex;
  align-items: center;
  border: none;
  cursor: pointer;
  transition: color 0.2s ease;
  font-size: 0.875rem;
}

.md-button--back:hover {
  color: #1976d2;
}

.md-button--back .material-icons {
  margin-right: 0.5rem;
}

.md-food-details__content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .md-food-details__content {
    flex-direction: row;
  }
}

.md-food-details__image-container {
  flex: 1;
}

.md-food-details__image {
  width: 100%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  margin-bottom: 1rem;
}

.md-food-details__image img {
  width: 100%;
  height: auto;
  display: block;
}

.md-food-details__placeholder {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #757575;
}

.md-food-details__placeholder .material-icons {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.md-food-details__categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.md-chip {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #eceff1;
  color: #546e7a;
}

.md-chip--highlight {
  background-color: #e3f2fd;
  color: #1976d2;
}

.md-food-details__info {
  flex: 1;
}

.md-food-details__title {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  color: #333;
}

.md-rating {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.md-rating--large .material-icons {
  font-size: 1.5rem;
}

.md-rating__stars {
  display: flex;
}

.md-rating__star--filled {
  color: #ffc107;
}

.md-rating__count {
  margin-left: 0.5rem;
  color: #757575;
  font-size: 0.875rem;
}

.md-food-details__price {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.md-food-details__current-price {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1976d2;
}

.md-food-details__original-price {
  margin-left: 0.75rem;
  text-decoration: line-through;
  color: #757575;
  font-size: 1.25rem;
}

.md-food-details__discount {
  margin-left: 0.75rem;
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.md-food-details__description {
  margin-bottom: 2rem;
  line-height: 1.6;
  color: #444;
}

.md-food-details__actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
}

.md-quantity-field {
  min-width: 150px;
}

.md-quantity-field__label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
  font-size: 0.875rem;
}

.md-quantity-field__controls {
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
}

.md-quantity-field__input {
  width: 50px;
  border: none;
  text-align: center;
  font-size: 1rem;
  padding: 0.5rem 0;
}

.md-quantity-field__input:focus {
  outline: none;
}

.md-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  font-size: 0.875rem;
}

.md-button--icon {
  background-color: transparent;
  color: #555;
  padding: 0.5rem;
}

.md-button--icon:hover:not(:disabled) {
  background-color: rgba(0,0,0,0.05);
}

.md-button--primary {
  background-color: #1976d2;
  color: white;
}

.md-button--primary:hover:not(:disabled) {
  background-color: #1565c0;
}

.md-button--large {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.md-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.md-button__icon {
  margin-right: 0.5rem;
}

.md-button__icon--spin {
  animation: spin 1s linear infinite;
}

.md-food-details__share {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.md-food-details__share-title {
  font-size: 1rem;
  margin-bottom: 0.75rem;
  color: #555;
}

.md-food-details__share-buttons {
  display: flex;
  gap: 0.5rem;
}

.md-button--share {
  background-color: #f5f5f5;
  color: #555;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  padding: 0;
}

.md-button--share:hover {
  background-color: #e0e0e0;
}

.md-error-state {
  text-align: center;
  padding: 4rem 1rem;
}

.md-error-state__icon {
  font-size: 4rem;
  color: #f44336;
  margin-bottom: 1rem;
}

.md-error-state__title {
  font-size: 1.75rem;
  margin-bottom: 1rem;
  color: #333;
}

.md-error-state__message {
  color: #757575;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.md-related-items {
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.md-related-items__title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.md-related-items__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.md-food-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.md-food-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.md-food-card__image {
  height: 150px;
  overflow: hidden;
}

.md-food-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.md-food-card:hover .md-food-card__image img {
  transform: scale(1.05);
}

.md-food-card__placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #757575;
}

.md-food-card__content {
  padding: 1rem;
}

.md-food-card__title {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.md-food-card__price {
  display: flex;
  align-items: baseline;
}

.md-food-card__current-price {
  font-weight: 600;
  color: #1976d2;
}

.md-food-card__original-price {
  margin-left: 0.5rem;
  text-decoration: line-through;
  color: #757575;
  font-size: 0.875rem;
}

.md-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #757575;
}

.md-empty-state .material-icons {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.md-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  max-width: 400px;
  z-index: 1000;
  animation: slide-in 0.3s ease;
}

@keyframes slide-in {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.md-notification--success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.md-notification--error {
  background-color: #ffebee;
  color: #c62828;
}

.md-notification--warning {
  background-color: #fff8e1;
  color: #ff8f00;
}

.md-notification--info {
  background-color: #e3f2fd;
  color: #1565c0;
}

.md-notification__icon {
  margin-right: 12px;
}

.md-notification__message {
  flex: 1;
}

.md-notification__close {
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.md-notification__close:hover {
  opacity: 1;
}
</style>
