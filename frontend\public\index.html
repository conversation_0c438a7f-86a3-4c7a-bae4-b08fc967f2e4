<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="theme-color" content="#FF9800" />
    <link rel="icon" href="./taco.png" type="image/x-icon" />
    <title>FoodDeli</title>

    <!-- Material Design 3 (Material You) -->
    <link href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Round" rel="stylesheet">
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Material Symbols -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />

    <!-- Google Fonts - Using Product Sans for headlines and Roboto for body -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Google+Sans+Text:wght@400;500&display=swap" rel="stylesheet">

    <style>
      :root {
        /* Material You - Orange Theme */
        --md-sys-color-primary: #FF9800;
        --md-sys-color-primary-container: #FFE0B2;
        --md-sys-color-on-primary: #FFFFFF;
        --md-sys-color-secondary: #F57C00;
        --md-sys-color-surface: #FFFFFF;
        --md-sys-color-surface-variant: #F5F5F5;
        --md-sys-color-background: #FAFAFA;
        
        /* Material You spacing units */
        --md-sys-spacing-track: 4px;
        --md-sys-spacing-small: 8px;
        --md-sys-spacing-medium: 16px;
        --md-sys-spacing-large: 24px;
        
        /* Material You shape scales */
        --md-sys-shape-corner-extra-small: 4px;
        --md-sys-shape-corner-small: 8px;
        --md-sys-shape-corner-medium: 12px;
        --md-sys-shape-corner-large: 16px;
        --md-sys-shape-corner-extra-large: 28px;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: 'Roboto', sans-serif;
        background-color: var(--md-sys-color-background);
        color: rgba(0, 0, 0, 0.87);
      }

      h1, h2, h3, h4, h5, h6 {
        font-family: 'Google Sans', sans-serif;
        letter-spacing: -0.022em;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but FoodDeli doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
