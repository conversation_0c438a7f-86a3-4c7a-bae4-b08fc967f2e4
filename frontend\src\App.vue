<template>
  <div id="app" :class="{'app--mobile': isMobile}">
    <div v-if="admin">
      <router-view></router-view>  
    </div>
    <div v-else class="app-container">
      <!-- Top App Bar -->
      <header class="md-top-app-bar">
        <div class="md-top-app-bar__row">
          <div class="md-top-app-bar__section">
            <button class="md-icon-button" v-if="isMobile" @click="toggleMenu">
              <span class="material-icons-round">menu</span>
            </button>
            <img src="/taco.png" alt="FoodDeli" class="md-top-app-bar__logo" />
            <h1 class="md-top-app-bar__title">FoodDeli</h1>
          </div>

          <!-- Search Bar - Show on Desktop, Hide on Mobile -->
          <div v-if="!isMobile" class="md-search-bar">
            <div class="md-search-bar__container">
              <span class="material-icons-round">search</span>
              <input type="text" v-model="searchQuery" @keyup.enter="performSearch" placeholder="Search menu items..." />
              <span class="material-icons-round">mic</span>
            </div>
          </div>

          <!-- Desktop Navigation -->
          <nav v-if="!isMobile" class="md-desktop-nav">
            <router-link to="/" class="md-button md-button--text">
              <span class="material-icons-round">home</span>
              <span>Home</span>
            </router-link>
            <router-link to="/menu" class="md-button md-button--text">
              <span class="material-icons-round">restaurant_menu</span>
              <span>Menu</span>
            </router-link>
            <router-link to="/promotions" class="md-button md-button--text">
              <span class="material-icons-round">local_offer</span>
              <span>Promos</span>
            </router-link>
            <router-link to="/about" class="md-button md-button--text">
              <span class="material-icons-round">info</span>
              <span>About</span>
            </router-link>
            <router-link v-if="user && user.role === 'admin'" to="/admin" class="md-button md-button--text">
              <span class="material-icons-round">admin_panel_settings</span>
              <span>Admin</span>
            </router-link>
          </nav>

          <div class="md-top-app-bar__section md-top-app-bar__section--end">
            <button v-if="!user" class="md-button md-button--text" @click="$router.push('/login')">
              <span class="material-icons-round">person_outline</span>
              <span>Login</span>
            </button>
            <button v-else class="md-button md-button--text" @click="handleLogout">
              <span class="material-icons-round">logout</span>
              <span>Logout</span>
            </button>
            <router-link v-if="user" to="/profile" class="md-button md-button--text">
              <span class="material-icons-round">person</span>
              <span>Profile</span>
            </router-link>
            <router-link to="/cart" class="md-button md-button--text" v-if="!isMobile">
              <span class="material-icons-round">shopping_cart</span>
              <span>Cart</span>
            </router-link>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="main-content">
        <!-- Search Bar - Show on Mobile Only -->
        <div v-if="isMobile" class="md-search-bar md-search-bar--mobile">
          <div class="md-search-bar__container">
            <span class="material-icons-round">search</span>
            <input type="text" v-model="searchQuery" @keyup.enter="performSearch" placeholder="Search menu items..." />
            <span class="material-icons-round">mic</span>
          </div>
        </div>
        
        <router-view></router-view>
      </main>

      <!-- Mobile Bottom Navigation -->
      <nav v-if="isMobile" class="md-bottom-navigation">
        <router-link to="/" class="md-bottom-navigation__item" exact>
          <span class="material-icons-round">home</span>
          <span>Home</span>
        </router-link>
        <router-link to="/menu" class="md-bottom-navigation__item">
          <span class="material-icons-round">restaurant_menu</span>
          <span>Menu</span>
        </router-link>
        <router-link to="/cart" class="md-bottom-navigation__item">
          <span class="material-icons-round">shopping_cart</span>
          <span>Cart</span>
        </router-link>
        <router-link to="/myorder" class="md-bottom-navigation__item">
          <span class="material-icons-round">receipt_long</span>
          <span>Orders</span>
        </router-link>
      </nav>

      <!-- Desktop Footer -->
      <FooterComponent v-if="!isMobile" />
    </div>
  </div>
</template>

<script>
import FooterComponent from './components/FooterComponent.vue';
import { mapActions, mapState, mapMutations } from 'vuex';

export default {
  name: 'App',
  components: {
    FooterComponent
  },

  data() {
    return {
      isMobile: false,
      showMenu: false,
      searchQuery: ''
    }
  },

  mounted() {
    this.getFoodsData();
    this.checkDeviceWidth();
    window.addEventListener('resize', this.checkDeviceWidth);
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.checkDeviceWidth);
  },

  computed: {
    ...mapState(["admin", "user"])
  },

  methods: {
    ...mapActions(["getFoodsData"]),
    ...mapMutations(["setUser"]),
    
    checkDeviceWidth() {
      this.isMobile = window.innerWidth <= 768;
    },

    handleLogout() {
      localStorage.removeItem('user');
      this.$store.commit('setUser', undefined);
      this.$router.push('/');
    },

    toggleMenu() {
      this.showMenu = !this.showMenu;
    },
    performSearch() {
      if (this.searchQuery.trim()) {
        // Navigate to menu page with search query
        this.$router.push({
          path: '/menu',
          query: { search: this.searchQuery }
        });
      }
    }
  }
}
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Material+Icons+Round');
@import "./assets/css/material-theme.css";

/* Reset any browser defaults */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

#app {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--md-sys-color-background);
  width: 100%;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
}

.md-top-app-bar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: var(--md-sys-color-surface);
  padding: var(--md-sys-spacing-medium) 0;
  box-shadow: var(--md-elevation-level2);
  margin: 0;
  width: 100%;
}

.md-top-app-bar__row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin: 0;
  padding: 0 5px;
  gap: 5px;
}

.md-top-app-bar__section {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 0;
}

.md-top-app-bar__section--end {
  margin-left: auto;
  padding-right: var(--md-sys-spacing-small);
}

.md-top-app-bar__logo {
  height: 32px;
  width: auto;
}

.md-top-app-bar__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-search-bar {
  flex: 0.5;
  max-width: 280px;
  margin: 0;
}

.md-search-bar--mobile {
  position: sticky;
  top: calc(var(--md-sys-spacing-medium) + 56px);
  z-index: 99;
  padding: var(--md-sys-spacing-medium);
  background-color: var(--md-sys-color-background);
}

.md-search-bar__container {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: var(--md-sys-color-surface-variant);
  padding: 4px 8px;
  border-radius: var(--md-sys-shape-corner-full);
  box-shadow: var(--md-elevation-level1);
}

.md-search-bar__container input {
  flex: 1;
  border: none;
  background: none;
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
  outline: none;
}

.md-desktop-nav {
  display: flex;
  gap: 5px;
}

.md-button.md-button--text {
  padding: 4px 8px;
}

.main-content {
  flex: 1;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-medium);
}

.md-bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  background-color: var(--md-sys-color-surface);
  padding: var(--md-sys-spacing-small) 0;
  box-shadow: var(--md-elevation-level2);
  z-index: 99;
}

.md-bottom-navigation__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: var(--md-sys-spacing-small);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  font-size: var(--md-sys-typescale-body-small-size);
}

.md-bottom-navigation__item--active {
  color: var(--md-sys-color-primary);
}

.md-icon-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  background: none;
  color: var(--md-sys-color-on-surface);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Desktop Styles */
@media (min-width: 769px) {
  .md-top-app-bar {
    padding: var(--md-sys-spacing-medium) 0;
  }

  .main-content {
    padding: var(--md-sys-spacing-large);
  }
}
</style>
